# 全局用户标签页管理功能实现

## 功能变更描述
将"隐藏/显示用户标签页"功能从每个工作区的独立功能改为全局功能，统一管理所有用户标签页的可见性。

## 实现内容

### 1. 全局用户标签页管理器
- **新增文件**: `src/utils/tabs.ts` 中的 `GlobalUserTabsVisibilityManager` 类
- **功能**:
  - 全局隐藏/显示所有用户标签页
  - 统一的状态管理（使用chrome.storage.local）
  - 自动过滤工作区占位符和扩展页面

### 2. 全局控制组件
- **新增文件**: `src/components/GlobalUserTabsControl.tsx`
- **功能**:
  - 显示全局用户标签页状态
  - 提供全局隐藏/显示切换按钮
  - 实时状态更新和计数显示

### 3. UI结构调整
- **文件**: `src/sidepanel/App.tsx`
- **修改**: 在侧边栏顶部添加全局用户标签页控制组件
- **位置**: 位于头部下方，工作区列表上方

### 4. 移除工作区级别控制
- **文件**: `src/components/WorkspaceItem.tsx`, `src/components/WorkspaceList.tsx`
- **修改**: 移除每个工作区的用户标签页控制功能
- **简化**: 减少组件复杂度，统一管理逻辑

## 测试步骤

### 测试场景1：全局功能基本测试
1. 打开Chrome扩展侧边栏
2. 在浏览器中打开几个用户标签页（非工作区网站）
3. 在侧边栏顶部找到"用户标签页管理"区域
4. 点击"隐藏标签页"按钮，验证用户标签页是否被隐藏
5. 点击"显示标签页"按钮，验证隐藏的标签页是否重新显示

### 测试场景2：工作区切换时的全局状态
1. 隐藏所有用户标签页
2. 创建并切换到不同工作区
3. 验证全局控制区域的状态显示是否正确
4. 在任意工作区中显示标签页
5. 验证状态在所有工作区中都保持一致

### 测试场景3：标签页过滤和计数
1. 打开多种类型的标签页：
   - 普通网站标签页（用户自行打开）
   - 工作区中添加的网站标签页
   - 工作区占位符页面
   - Chrome扩展页面
2. 验证全局控制只影响真正的用户标签页
3. 确认工作区管理的标签页不会被隐藏
4. 检查计数显示是否准确（显示/隐藏标签页数量）

## 预期结果

### 功能特性
- **全局控制**: 一个按钮控制所有真正的用户标签页的可见性
- **智能过滤**: 自动排除工作区管理的标签页、占位符、扩展页面和固定标签页
- **工作区保护**: 不会影响工作区中用户添加的网站标签页
- **实时计数**: 准确显示可见和隐藏的用户标签页数量
- **状态持久化**: 隐藏状态在浏览器重启后保持

### UI体验
- **统一位置**: 全局控制位于侧边栏顶部，易于访问
- **清晰状态**: 按钮文字和图标明确表示当前状态和操作
- **即时反馈**: 操作后立即更新计数和状态显示
- **简化界面**: 移除工作区级别的重复控制，界面更简洁

## 修复的问题

### 1. UI布局问题修复
- **响应式设计**: 优化小屏幕显示，按钮文字在小屏幕上隐藏
- **布局结构**: 改为两行布局，标题和按钮一行，状态信息一行
- **视觉层次**: 使用不同颜色区分不同类型的计数信息

### 2. 计数逻辑修复
- **准确计算**: 修复了可见标签页数量可能为负数的问题
- **状态验证**: 自动清理无效的隐藏标签页ID
- **窗口过滤**: 只计算当前窗口中的用户标签页
- **工作区标签页保护**: 不会隐藏工作区中用户添加的网站标签页
- **实时同步**: 确保显示的数据与实际状态一致

### 3. 调试工具
- **开发环境调试**: 添加详细的状态调试信息
- **自动清理**: 自动清理无效的隐藏标签页引用
- **状态诊断**: 提供完整的标签页分类和状态信息

## 技术细节

### 状态管理优化
1. **精确计数**: 分别计算可见和隐藏的用户标签页数量
2. **状态验证**: 每次加载时验证隐藏标签页是否仍然存在
3. **自动清理**: 清理已关闭的标签页引用
4. **窗口感知**: 只操作当前窗口中的标签页

### UI/UX改进
- **清晰布局**: 两行布局避免内容拥挤
- **颜色编码**: 不同状态使用不同颜色
- **响应式**: 适配不同屏幕尺寸
- **即时反馈**: 操作后立即更新显示
