# 继续隐藏功能实现

## 功能需求

用户反馈：当已经隐藏了一些标签页后，又打开了新的标签页，希望能够"继续隐藏"这些新标签页，而不是先显示所有隐藏的标签页再重新隐藏。

## 解决方案

### 1. 智能操作模式

实现三种智能操作模式，根据当前状态自动判断应该执行的操作：

#### 操作类型
1. **隐藏 (hide)**: 没有隐藏标签页时，隐藏所有可见的用户标签页
2. **继续隐藏 (continue_hide)**: 有隐藏标签页且有新的可见标签页时，继续隐藏新标签页
3. **显示 (show)**: 有隐藏标签页但没有可见标签页时，显示所有隐藏标签页

#### 判断逻辑
```typescript
if (hasHiddenTabs && hasVisibleTabs) {
  // 既有隐藏的又有可见的 -> 继续隐藏
  actionType = 'continue_hide';
} else if (hasHiddenTabs && !hasVisibleTabs) {
  // 只有隐藏的，没有可见的 -> 显示
  actionType = 'show';
} else {
  // 没有隐藏的，只有可见的 -> 隐藏
  actionType = 'hide';
}
```

### 2. 实现细节

#### 2.1 数据结构扩展
- **文件**: `src/utils/tabs.ts`
- **扩展**: `getGlobalUserTabsState` 返回值

```typescript
interface GlobalUserTabsState {
  isHidden: boolean;
  hiddenTabIds: number[];
  totalUserTabs: number;
  visibleUserTabs: number;
  canContinueHiding: boolean;  // 新增
  actionType: 'hide' | 'continue_hide' | 'show';  // 新增
}
```

#### 2.2 新增方法
- **方法**: `continueHideUserTabs()`
- **功能**: 只隐藏当前可见的用户标签页，保留已隐藏的标签页

```typescript
static async continueHideUserTabs(): Promise<OperationResult<number[]>> {
  // 1. 获取当前状态
  // 2. 筛选出可见的用户标签页（排除已隐藏的）
  // 3. 移动到隐藏窗口
  // 4. 更新隐藏状态（合并新的标签页ID）
}
```

#### 2.3 智能切换逻辑
- **方法**: `toggleGlobalUserTabsVisibility()`
- **改进**: 根据 `actionType` 执行不同操作

```typescript
switch (actionType) {
  case 'show':
    return await this.showAllUserTabs();
  case 'continue_hide':
    return await this.continueHideUserTabs();
  case 'hide':
  default:
    return await this.hideAllUserTabs();
}
```

### 3. UI改进

#### 3.1 智能按钮显示
- **文件**: `src/components/GlobalUserTabsControl.tsx`

#### 按钮状态和颜色
1. **隐藏按钮**: 蓝色 + EyeOff图标 + "隐藏"文字
2. **继续隐藏按钮**: 橙色 + EyeOff图标 + "继续隐藏"文字 + 黄色指示器
3. **显示按钮**: 绿色 + Eye图标 + "显示"文字

#### 3.2 状态指示器
- **继续隐藏模式**: 显示黄色脉冲指示器，提示有新标签页可继续隐藏
- **工具提示**: 根据操作类型显示不同的提示信息

## 4. 使用场景

### 场景1：初次使用
```
用户状态: 打开了5个标签页
显示: [•] 用户标签页 5/5     [隐藏]
操作: 点击"隐藏" → 隐藏所有5个标签页
```

### 场景2：继续隐藏（核心场景）
```
用户状态: 已隐藏3个标签页，又新开了2个标签页
显示: [•] 用户标签页 2/5 • 3隐藏     [继续隐藏] ●
操作: 点击"继续隐藏" → 只隐藏新的2个标签页，保留之前隐藏的3个
结果: [•] 用户标签页 0/5 • 5隐藏     [显示]
```

### 场景3：显示所有
```
用户状态: 所有标签页都被隐藏
显示: [•] 用户标签页 0/5 • 5隐藏     [显示]
操作: 点击"显示" → 显示所有5个隐藏的标签页
```

### 场景4：混合操作
```
1. 用户打开5个标签页 → [隐藏]
2. 隐藏所有标签页 → [显示]
3. 显示所有标签页 → [隐藏]
4. 隐藏3个标签页，新开2个 → [继续隐藏] ●
5. 继续隐藏2个新标签页 → [显示]
```

## 5. 技术优势

### 5.1 用户体验
- **避免不必要的操作**: 不需要先显示再隐藏
- **操作意图明确**: 按钮文字清楚表达将要执行的操作
- **状态可视化**: 通过颜色和指示器清楚显示当前状态

### 5.2 技术实现
- **状态管理**: 准确跟踪隐藏和可见标签页的状态
- **智能判断**: 自动判断用户意图，提供最合适的操作
- **增量操作**: 继续隐藏只操作新标签页，提高效率

### 5.3 向后兼容
- **API兼容**: 保持现有API接口不变
- **功能增强**: 在原有功能基础上增加智能判断
- **渐进增强**: 用户无需学习新操作，自然使用即可

## 6. 测试验证

### 6.1 功能测试
1. **基础隐藏**: 验证初次隐藏功能正常
2. **继续隐藏**: 验证继续隐藏只影响新标签页
3. **显示功能**: 验证显示功能恢复所有隐藏标签页
4. **状态同步**: 验证UI状态与实际标签页状态一致

### 6.2 边界测试
1. **空状态**: 没有用户标签页时的处理
2. **全隐藏**: 所有标签页都隐藏时的处理
3. **并发操作**: 快速连续操作的处理
4. **异常恢复**: 标签页被外部关闭时的状态恢复

### 6.3 用户体验测试
1. **操作流畅性**: 验证操作响应速度
2. **状态清晰性**: 验证用户能理解当前状态
3. **操作预期性**: 验证操作结果符合用户预期

## 7. 预期效果

实现后，用户将享受到：
- 🎯 **精确控制**: 只隐藏想隐藏的标签页
- ⚡ **高效操作**: 避免不必要的显示-隐藏循环
- 🎨 **直观界面**: 清楚的状态显示和操作提示
- 🧠 **智能判断**: 系统自动判断用户意图，提供最合适的操作
