import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Plus } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';

interface HeaderProps {
  activeWorkspace: WorkSpace | null;
  onSettingsClick: () => void;
  onCreateWorkspaceClick: () => void;
}

/**
 * 头部组件
 */
const Header: React.FC<HeaderProps> = ({ activeWorkspace, onSettingsClick, onCreateWorkspaceClick }) => {
  return (
    <div className="flex items-center justify-between p-3 border-b border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900">
      {/* 左侧：Logo和当前活跃工作区 */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <Rocket className="w-5 h-5 text-blue-500" />
          {/* 移除了 WorkSpace Pro 标题 */}
        </div>

        {/* 当前活跃工作区指示器 */}
        {activeWorkspace && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{activeWorkspace.icon}</span>
            <span className="text-sm text-slate-300 font-medium">
              {activeWorkspace.name}
            </span>
            <div
              className="w-2 h-2 rounded-full bg-green-500 animate-pulse"
              title="活跃工作区"
            />
          </div>
        )}
      </div>

      {/* 右侧：新建工作区按钮和设置按钮 */}
      <div className="flex items-center gap-2">
        {/* 新建工作区按钮 */}
        <button
          onClick={onCreateWorkspaceClick}
          className="flex items-center gap-2 px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors duration-200"
          title="新建工作区"
        >
          <Plus className="w-4 h-4" />
          <span className="hidden sm:inline">新建工作区</span>
        </button>

        {/* 设置按钮 */}
        <button
          onClick={onSettingsClick}
          className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200 group"
          title="设置"
        >
          <Settings className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors duration-200" />
        </button>
      </div>
    </div>
  );
};

export default Header;
