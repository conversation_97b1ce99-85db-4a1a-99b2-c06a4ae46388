import React from 'react';
import { <PERSON><PERSON><PERSON>, Rocket, Plus } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';

interface HeaderProps {
  activeWorkspace: WorkSpace | null;
  onSettingsClick: () => void;
  onCreateWorkspaceClick: () => void;
}

/**
 * 头部组件
 */
const Header: React.FC<HeaderProps> = ({ activeWorkspace, onSettingsClick, onCreateWorkspaceClick }) => {
  return (
    <div className="flex items-center justify-between px-2 py-2 border-b border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900">
      {/* 左侧：Logo和当前活跃工作区 - 消除左侧间距 */}
      <div className="flex items-center gap-2 min-w-0 flex-1">
        <Rocket className="w-4 h-4 text-blue-500 flex-shrink-0" />

        {/* 当前活跃工作区指示器 */}
        {activeWorkspace && (
          <div className="flex items-center gap-2 min-w-0">
            <span className="text-base flex-shrink-0">{activeWorkspace.icon}</span>
            <span className="text-sm text-slate-300 font-medium truncate">
              {activeWorkspace.name}
            </span>
            <div
              className="w-1.5 h-1.5 rounded-full bg-green-500 animate-pulse flex-shrink-0"
              title="活跃工作区"
            />
          </div>
        )}
      </div>

      {/* 右侧：纯图标按钮组 */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {/* 新建工作区按钮 - 纯图标 */}
        <button
          onClick={onCreateWorkspaceClick}
          className="flex items-center justify-center w-7 h-7 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-200"
          title="新建工作区"
        >
          <Plus className="w-4 h-4" />
        </button>

        {/* 设置按钮 - 纯图标 */}
        <button
          onClick={onSettingsClick}
          className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200 group"
          title="设置"
        >
          <Settings className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors duration-200" />
        </button>
      </div>
    </div>
  );
};

export default Header;
