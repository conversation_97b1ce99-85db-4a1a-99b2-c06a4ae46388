import React, { useState } from 'react';
import { Settings, Rocket, Plus, Monitor, ChevronDown } from 'lucide-react';
import { WorkSpace } from '@/types/workspace';

interface HeaderProps {
  activeWorkspace: WorkSpace | null;
  workspaces: WorkSpace[];
  onSettingsClick: () => void;
  onCreateWorkspaceClick: () => void;
  onAddCurrentTab: (workspaceId: string) => void;
}

/**
 * 头部组件
 */
const Header: React.FC<HeaderProps> = ({ activeWorkspace, workspaces, onSettingsClick, onCreateWorkspaceClick, onAddCurrentTab }) => {
  const [showWorkspaceDropdown, setShowWorkspaceDropdown] = useState(false);
  return (
    <div className="flex items-center justify-between px-2 py-2 border-b border-slate-700 bg-gradient-to-r from-slate-800 to-slate-900">
      {/* 左侧：当前活跃工作区 */}
      <div className="flex items-center gap-2 min-w-0 flex-1">
        {/* 当前活跃工作区指示器 */}
        {activeWorkspace ? (
          <div className="flex items-center gap-2 min-w-0">
            <span className="text-base flex-shrink-0">{activeWorkspace.icon}</span>
            <span className="text-sm text-slate-300 font-medium truncate">
              {activeWorkspace.name}
            </span>
            <div
              className="w-1.5 h-1.5 rounded-full bg-green-500 animate-pulse flex-shrink-0"
              title="活跃工作区"
            />
          </div>
        ) : (
          <span className="text-sm text-slate-300 font-medium">
            WorkSpace Pro
          </span>
        )}
      </div>

      {/* 右侧：纯图标按钮组 */}
      <div className="flex items-center gap-1 flex-shrink-0">
        {/* 添加当前标签页按钮 - 带下拉选择工作区 */}
        <div className="relative">
          <button
            onClick={() => {
              if (workspaces.length === 1) {
                onAddCurrentTab(workspaces[0].id);
              } else {
                setShowWorkspaceDropdown(!showWorkspaceDropdown);
              }
            }}
            className="flex items-center justify-center w-7 h-7 bg-green-600 hover:bg-green-700 text-white rounded transition-colors duration-200"
            title={workspaces.length === 1 ? `添加当前标签页到 ${workspaces[0].name}` : "选择工作区添加当前标签页"}
            disabled={workspaces.length === 0}
          >
            <Monitor className="w-3 h-3" />
            {workspaces.length > 1 && (
              <ChevronDown className="w-2 h-2 ml-0.5" />
            )}
          </button>

          {/* 工作区选择下拉菜单 */}
          {showWorkspaceDropdown && workspaces.length > 1 && (
            <div className="dropdown-menu">
              {workspaces.map((workspace) => (
                <button
                  key={workspace.id}
                  onClick={() => {
                    onAddCurrentTab(workspace.id);
                    setShowWorkspaceDropdown(false);
                  }}
                  className={`dropdown-item ${workspace.isActive ? 'bg-blue-600/20' : ''}`}
                >
                  <span className="text-base">{workspace.icon}</span>
                  <span>{workspace.name}</span>
                  {workspace.isActive && (
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full ml-auto" />
                  )}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 新建工作区按钮 - 纯图标 */}
        <button
          onClick={onCreateWorkspaceClick}
          className="flex items-center justify-center w-7 h-7 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors duration-200"
          title="新建工作区"
        >
          <Plus className="w-4 h-4" />
        </button>

        {/* 设置按钮 - 纯图标 */}
        <button
          onClick={onSettingsClick}
          className="flex items-center justify-center w-7 h-7 hover:bg-slate-700 rounded transition-colors duration-200 group"
          title="设置"
        >
          <Settings className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors duration-200" />
        </button>
      </div>
    </div>
  );
};

export default Header;
