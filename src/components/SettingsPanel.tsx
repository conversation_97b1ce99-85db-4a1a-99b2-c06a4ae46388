import React from 'react';
import { X, Download, Upload, Trash2 } from 'lucide-react';
import { StorageManager } from '@/utils/storage';

interface SettingsPanelProps {
  onClose: () => void;
}

/**
 * 设置面板组件 - 仅数据管理
 */
const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {

  /**
   * 导出数据
   */
  const handleExport = async () => {
    try {
      const result = await StorageManager.exportData();
      if (result.success) {
        const blob = new Blob([result.data!], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `workspace-pro-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    } catch (error) {
      console.error('Failed to export data:', error);
    }
  };

  /**
   * 导入数据
   */
  const handleImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        try {
          const text = await file.text();
          const result = await StorageManager.importData(text);
          if (result.success) {
            window.location.reload();
          }
        } catch (error) {
          console.error('Failed to import data:', error);
        }
      }
    };
    input.click();
  };

  /**
   * 清除所有数据
   */
  const handleClearAll = async () => {
    if (confirm('确定要清除所有数据吗？此操作无法撤销。')) {
      try {
        await StorageManager.clearAll();
        window.location.reload();
      } catch (error) {
        console.error('Failed to clear data:', error);
      }
    }
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in max-w-md">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            数据管理
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 数据管理内容 */}
        <div className="space-y-4">
          <button
            onClick={handleExport}
            className="w-full btn-secondary justify-center"
          >
            <Download className="w-4 h-4" />
            导出数据
          </button>
          <button
            onClick={handleImport}
            className="w-full btn-secondary justify-center"
          >
            <Upload className="w-4 h-4" />
            导入数据
          </button>
          <button
            onClick={handleClearAll}
            className="w-full btn-danger justify-center"
          >
            <Trash2 className="w-4 h-4" />
            清除所有数据
          </button>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end mt-6 pt-4 border-t border-slate-700">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsPanel;
