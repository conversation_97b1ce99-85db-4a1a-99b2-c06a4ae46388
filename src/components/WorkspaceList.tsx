import React, { useState } from 'react';
import { WorkSpace } from '@/types/workspace';
import WorkspaceItem from './WorkspaceItem';

interface WorkspaceListProps {
  workspaces: WorkSpace[];
  activeWorkspaceId: string | null;
  onSwitchWorkspace: (workspaceId: string) => void;
  onUpdateWorkspace: (id: string, updates: { name?: string; icon?: string; color?: string }) => void;
  onDeleteWorkspace: (id: string) => void;
  onAddCurrentTab: (workspaceId: string) => void;
  onAddWebsiteUrl: (workspaceId: string, url: string) => void;
  onRemoveWebsite: (workspaceId: string, websiteId: string) => void;
  onUpdateWebsite: (workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => void;
  onReorderWorkspaces: (workspaceIds: string[]) => void;
  onReorderWebsites: (workspaceId: string, websiteIds: string[]) => void;
  onTogglePin?: (workspaceId: string, websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (workspaceId: string, websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (workspaceId: string, websiteIds: string[]) => void;
}

/**
 * 工作区列表组件
 */
const WorkspaceList: React.FC<WorkspaceListProps> = ({
  workspaces,
  activeWorkspaceId,
  onSwitchWorkspace,
  onUpdateWorkspace,
  onDeleteWorkspace,
  onAddCurrentTab,
  onAddWebsiteUrl,
  onRemoveWebsite,
  onUpdateWebsite,
  onReorderWorkspaces: _onReorderWorkspaces,
  onReorderWebsites,
  onTogglePin,
  onBatchPin,
  onBatchDelete,
}) => {
  const [expandedWorkspaceId, setExpandedWorkspaceId] = useState<string | null>(
    activeWorkspaceId
  );

  /**
   * 处理工作区展开/折叠
   */
  const handleToggleExpand = (workspaceId: string) => {
    setExpandedWorkspaceId(
      expandedWorkspaceId === workspaceId ? null : workspaceId
    );
  };

  /**
   * 处理工作区点击
   */
  const handleWorkspaceClick = (workspaceId: string) => {
    // 如果点击的是当前活跃工作区，则切换展开状态
    if (workspaceId === activeWorkspaceId) {
      handleToggleExpand(workspaceId);
    } else {
      // 否则切换工作区并展开
      onSwitchWorkspace(workspaceId);
      setExpandedWorkspaceId(workspaceId);
    }
  };

  return (
    <div className="flex-1 overflow-y-auto px-2 py-3 space-y-2">
      {workspaces.map((workspace) => (
        <WorkspaceItem
          key={workspace.id}
          workspace={workspace}
          isActive={workspace.id === activeWorkspaceId}
          isExpanded={workspace.id === expandedWorkspaceId}
          onWorkspaceClick={() => handleWorkspaceClick(workspace.id)}
          onToggleExpand={() => handleToggleExpand(workspace.id)}
          onUpdateWorkspace={(updates) => onUpdateWorkspace(workspace.id, updates)}
          onDeleteWorkspace={() => onDeleteWorkspace(workspace.id)}
          onAddCurrentTab={() => onAddCurrentTab(workspace.id)}
          onAddWebsiteUrl={(url) => onAddWebsiteUrl(workspace.id, url)}
          onRemoveWebsite={(websiteId) => onRemoveWebsite(workspace.id, websiteId)}
          onUpdateWebsite={(websiteId, updates) => onUpdateWebsite(workspace.id, websiteId, updates)}
          onReorderWebsites={(websiteIds) => onReorderWebsites(workspace.id, websiteIds)}
          onTogglePin={onTogglePin ? (websiteId, isPinned) => onTogglePin(workspace.id, websiteId, isPinned) : undefined}
          onBatchPin={onBatchPin ? (websiteIds, isPinned) => onBatchPin(workspace.id, websiteIds, isPinned) : undefined}
          onBatchDelete={onBatchDelete ? (websiteIds) => onBatchDelete(workspace.id, websiteIds) : undefined}
        />
      ))}
    </div>
  );
};

export default WorkspaceList;
