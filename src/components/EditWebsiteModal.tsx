import React, { useState } from 'react';
import { X, Save, ExternalLink } from 'lucide-react';
import { Website } from '@/types/workspace';
import { URL_REGEX } from '@/utils/constants';

interface EditWebsiteModalProps {
  website: Website;
  onClose: () => void;
  onSave: (updates: { url?: string; title?: string; isPinned?: boolean }) => void;
}

/**
 * 编辑网站模态框
 */
const EditWebsiteModal: React.FC<EditWebsiteModalProps> = ({
  website,
  onClose,
  onSave,
}) => {
  const [url, setUrl] = useState(website.url);
  const [title, setTitle] = useState(website.title);
  const [error, setError] = useState('');

  /**
   * 验证URL格式
   */
  const validateUrl = (inputUrl: string): boolean => {
    if (!inputUrl.trim()) {
      setError('请输入网站URL');
      return false;
    }

    // 如果没有协议，自动添加https://
    let formattedUrl = inputUrl.trim();
    if (!formattedUrl.startsWith('http://') && !formattedUrl.startsWith('https://')) {
      formattedUrl = 'https://' + formattedUrl;
    }

    if (!URL_REGEX.test(formattedUrl)) {
      setError('请输入有效的网站URL');
      return false;
    }

    setError('');
    setUrl(formattedUrl);
    return true;
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateUrl(url)) {
      return;
    }

    if (!title.trim()) {
      setError('请输入网站标题');
      return;
    }

    const updates: { url?: string; title?: string; isPinned?: boolean } = {};

    if (url !== website.url) {
      updates.url = url;
    }

    if (title.trim() !== website.title) {
      updates.title = title.trim();
    }

    // 工作区网站默认都是固定的，确保isPinned为true
    updates.isPinned = true;

    onSave(updates);
  };

  /**
   * 处理URL输入变化
   */
  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputUrl = e.target.value;
    setUrl(inputUrl);
    
    // 清除错误信息
    if (error) {
      setError('');
    }
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  /**
   * 检查是否有更改
   */
  const hasChanges =
    url !== website.url ||
    title.trim() !== website.title;

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            编辑网站
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          {/* 网站标题 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              网站标题
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="输入网站标题..."
              className="input-field w-full"
              autoFocus
            />
          </div>

          {/* URL输入 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              网站URL
            </label>
            <div className="relative">
              <input
                type="text"
                value={url}
                onChange={handleUrlChange}
                placeholder="输入网站URL，例如：google.com"
                className={`input-field w-full pr-10 ${error ? 'border-red-500' : ''}`}
              />
              <ExternalLink className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
            </div>
            {error && (
              <p className="text-red-400 text-sm mt-1">{error}</p>
            )}
          </div>

          {/* 说明信息 */}
          <div className="mb-6 p-3 bg-blue-600/10 border border-blue-600/20 rounded-lg">
            <p className="text-sm text-blue-300">
              💡 工作区网站会自动固定，确保在切换工作区时正确识别和管理
            </p>
          </div>

          {/* 预览 */}
          <div className="mb-6 p-3 bg-slate-700 rounded-lg">
            <div className="text-sm text-slate-200 mb-2">预览</div>
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
                <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="text-sm text-white truncate">
                  {title || '网站标题'}
                </div>
                <div className="text-xs text-slate-400 truncate">
                  {url || 'https://example.com'}
                </div>
              </div>
              {/* 工作区网站默认固定 */}
              <div className="text-blue-400 text-xs">📌</div>
            </div>
          </div>

          {/* 按钮 */}
          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!title.trim() || !url.trim() || !hasChanges}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              保存更改
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditWebsiteModal;
