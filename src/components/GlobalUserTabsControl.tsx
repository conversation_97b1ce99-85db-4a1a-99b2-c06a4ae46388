import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';

interface GlobalUserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  totalUserTabs: number;
  loading: boolean;
}

/**
 * 全局用户标签页控制组件
 */
const GlobalUserTabsControl: React.FC = () => {
  const [state, setState] = useState<GlobalUserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    totalUserTabs: 0,
    loading: false,
  });

  /**
   * 加载全局用户标签页状态
   */
  const loadGlobalState = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await GlobalUserTabsVisibilityManager.getGlobalUserTabsState();

      if (result.success) {
        setState({
          isHidden: result.data!.isHidden,
          hiddenTabsCount: result.data!.hiddenTabIds.length,
          totalUserTabs: result.data!.totalUserTabs,
          loading: false,
        });
      } else {
        console.error('获取全局用户标签页状态失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('加载全局用户标签页状态时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 切换全局用户标签页显示状态
   */
  const handleToggleVisibility = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await GlobalUserTabsVisibilityManager.toggleGlobalUserTabsVisibility();

      if (result.success) {
        const { action, tabIds } = result.data!;
        console.log(`✅ 全局用户标签页${action === 'hidden' ? '隐藏' : '显示'}成功，影响 ${tabIds.length} 个标签页`);
        
        // 重新加载状态
        await loadGlobalState();

        // 发送全局状态更新事件
        try {
          const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
          WorkspaceStateSync.sendWorkspaceStateUpdate('global', 'userTabsVisibility');
        } catch (error) {
          console.error('发送全局用户标签页状态变化事件失败:', error);
        }
      } else {
        console.error('切换全局用户标签页显示状态失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换全局用户标签页显示状态时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 监听全局状态变化事件
   */
  useEffect(() => {
    const setupStateListener = async () => {
      try {
        const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
        
        const cleanup = WorkspaceStateSync.addStateListener((workspaceId, eventType) => {
          // 当有状态变化时，重新加载全局状态
          if (eventType === 'userTabsVisibility' || eventType === 'switch') {
            loadGlobalState();
          }
        });

        return cleanup;
      } catch (error) {
        console.error('设置全局状态监听器失败:', error);
        return () => {};
      }
    };

    let cleanup: (() => void) | undefined;
    
    setupStateListener().then(cleanupFn => {
      cleanup = cleanupFn;
    });

    // 初始加载状态
    loadGlobalState();

    // 清理事件监听器
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  // 计算显示的标签页数量
  const visibleUserTabs = state.totalUserTabs - state.hiddenTabsCount;

  return (
    <div className="p-4 border-b border-slate-700 bg-slate-800">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-blue-500"></div>
            <span className="text-sm font-medium text-slate-200">
              用户标签页管理
            </span>
          </div>
          
          {/* 状态指示器 */}
          <div className="flex items-center gap-2 text-xs text-slate-400">
            <span>
              显示: {visibleUserTabs} 个
            </span>
            {state.isHidden && state.hiddenTabsCount > 0 && (
              <>
                <span>•</span>
                <span>
                  隐藏: {state.hiddenTabsCount} 个
                </span>
              </>
            )}
          </div>
        </div>

        {/* 切换按钮 */}
        <button
          onClick={handleToggleVisibility}
          disabled={state.loading || state.totalUserTabs === 0}
          className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
            state.loading || state.totalUserTabs === 0
              ? 'opacity-50 cursor-not-allowed bg-slate-700'
              : state.isHidden
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
          title={
            state.totalUserTabs === 0
              ? '没有用户标签页'
              : state.isHidden
              ? `显示 ${state.hiddenTabsCount} 个隐藏的用户标签页`
              : `隐藏 ${visibleUserTabs} 个用户标签页`
          }
        >
          {state.loading ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : state.isHidden ? (
            <Eye className="w-4 h-4" />
          ) : (
            <EyeOff className="w-4 h-4" />
          )}
          
          <span>
            {state.loading
              ? '处理中...'
              : state.isHidden
              ? '显示标签页'
              : '隐藏标签页'
            }
          </span>
        </button>
      </div>
    </div>
  );
};

export default GlobalUserTabsControl;
