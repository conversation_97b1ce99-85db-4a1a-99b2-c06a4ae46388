import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';

interface GlobalUserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  totalUserTabs: number;
  visibleUserTabs: number;
  canContinueHiding: boolean;
  actionType: 'hide' | 'continue_hide' | 'show';
  loading: boolean;
}

/**
 * 全局用户标签页控制组件
 */
const GlobalUserTabsControl: React.FC = () => {
  const [state, setState] = useState<GlobalUserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    totalUserTabs: 0,
    visibleUserTabs: 0,
    canContinueHiding: false,
    actionType: 'hide',
    loading: false,
  });

  /**
   * 加载全局用户标签页状态
   */
  const loadGlobalState = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 开发环境调试
      if (process.env.NODE_ENV === 'development') {
        try {
          const { GlobalTabsDebugger } = await import('@/utils/debugGlobalTabs');
          await GlobalTabsDebugger.cleanupInvalidHiddenTabs();
        } catch (error) {
          console.log('调试工具加载失败:', error);
        }
      }

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await GlobalUserTabsVisibilityManager.getGlobalUserTabsState();

      if (result.success) {
        const data = result.data!;
        setState({
          isHidden: data.isHidden,
          hiddenTabsCount: data.hiddenTabIds.length,
          totalUserTabs: data.totalUserTabs,
          visibleUserTabs: data.visibleUserTabs,
          canContinueHiding: data.canContinueHiding,
          actionType: data.actionType,
          loading: false,
        });

        // 开发环境输出调试信息
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 全局用户标签页状态更新:', {
            isHidden: data.isHidden,
            hiddenTabsCount: data.hiddenTabIds.length,
            totalUserTabs: data.totalUserTabs,
            visibleUserTabs: data.visibleUserTabs,
            canContinueHiding: data.canContinueHiding,
            actionType: data.actionType,
          });
        }
      } else {
        console.error('获取全局用户标签页状态失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('加载全局用户标签页状态时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 标准隐藏/显示切换
   */
  const handleToggleVisibility = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 安全性检查：确保有用户标签页可以操作
      if (state.totalUserTabs === 0) {
        console.warn('⚠️ 没有用户标签页可以操作，取消切换操作');
        setState(prev => ({ ...prev, loading: false }));
        return;
      }

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');

      // 根据当前状态决定操作
      let result;
      if (state.isHidden) {
        // 当前是隐藏状态，执行显示操作
        if (state.hiddenTabsCount === 0) {
          console.warn('⚠️ 没有隐藏的标签页可以显示，取消操作');
          setState(prev => ({ ...prev, loading: false }));
          return;
        }

        result = await GlobalUserTabsVisibilityManager.showAllUserTabs();
        if (result.success) {
          console.log(`✅ 显示所有用户标签页成功，影响 ${result.data!.length} 个标签页`);
        }
      } else {
        // 当前是显示状态，执行隐藏操作
        if (state.visibleUserTabs === 0) {
          console.warn('⚠️ 没有可见的用户标签页可以隐藏，取消操作');
          setState(prev => ({ ...prev, loading: false }));
          return;
        }

        result = await GlobalUserTabsVisibilityManager.hideAllUserTabs();
        if (result.success) {
          console.log(`✅ 隐藏所有用户标签页成功，影响 ${result.data!.length} 个标签页`);
        }
      }

      if (result.success) {
        // 重新加载状态
        await loadGlobalState();

        // 发送全局状态更新事件
        try {
          const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
          WorkspaceStateSync.sendWorkspaceStateUpdate('global', 'userTabsVisibility');
        } catch (error) {
          console.error('发送全局用户标签页状态变化事件失败:', error);
        }
      } else {
        console.error('切换全局用户标签页显示状态失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换全局用户标签页显示状态时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 继续隐藏当前可见的用户标签页
   */
  const handleContinueHide = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 安全性检查：确保有可见的用户标签页可以继续隐藏
      if (state.visibleUserTabs === 0) {
        console.warn('⚠️ 没有可见的用户标签页可以继续隐藏，取消操作');
        setState(prev => ({ ...prev, loading: false }));
        return;
      }

      if (!state.canContinueHiding) {
        console.warn('⚠️ 当前状态不允许继续隐藏操作，取消操作');
        setState(prev => ({ ...prev, loading: false }));
        return;
      }

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await GlobalUserTabsVisibilityManager.continueHideUserTabs();

      if (result.success) {
        console.log(`✅ 继续隐藏用户标签页成功，影响 ${result.data!.length} 个标签页`);

        // 重新加载状态
        await loadGlobalState();

        // 发送全局状态更新事件
        try {
          const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
          WorkspaceStateSync.sendWorkspaceStateUpdate('global', 'userTabsVisibility');
        } catch (error) {
          console.error('发送全局用户标签页状态变化事件失败:', error);
        }
      } else {
        console.error('继续隐藏用户标签页失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('继续隐藏用户标签页时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 监听全局状态变化事件
   */
  useEffect(() => {
    const setupStateListener = async () => {
      try {
        const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');

        const cleanup = WorkspaceStateSync.addStateListener((workspaceId, eventType) => {
          // 当有状态变化时，重新加载全局状态
          if (eventType === 'userTabsVisibility' || eventType === 'switch') {
            console.log(`🔄 收到状态变化通知: ${workspaceId} - ${eventType}`);
            loadGlobalState();
          }
        });

        return cleanup;
      } catch (error) {
        console.error('设置全局状态监听器失败:', error);
        return () => {};
      }
    };

    // 设置定期状态检查（作为备用机制）
    const setupPeriodicCheck = () => {
      const interval = setInterval(() => {
        // 每3秒检查一次状态，确保UI与实际状态同步
        console.log('🔍 定期检查全局用户标签页状态');
        loadGlobalState();
      }, 3000);

      return () => clearInterval(interval);
    };

    let cleanup: (() => void) | undefined;
    let periodicCleanup: (() => void) | undefined;

    setupStateListener().then(cleanupFn => {
      cleanup = cleanupFn;
    });

    periodicCleanup = setupPeriodicCheck();

    // 初始加载状态
    loadGlobalState();

    // 清理事件监听器
    return () => {
      if (cleanup) {
        cleanup();
      }
      if (periodicCleanup) {
        periodicCleanup();
      }
    };
  }, []);

  // 使用从后端获取的准确数据
  const { visibleUserTabs, hiddenTabsCount, totalUserTabs, canContinueHiding } = state;

  return (
    <div className="px-2 py-2 border-b border-slate-700 bg-slate-800">
      {/* 充满布局 - 消除左侧间距 */}
      <div className="flex items-center justify-between w-full">
        {/* 左侧：紧凑状态显示 */}
        <div className="flex items-center gap-2">
          <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>

          {/* 数字状态显示 */}
          <div className="flex items-center gap-1.5 text-xs">
            <span className="text-blue-400 font-medium">{visibleUserTabs}</span>
            <span className="text-slate-500">/</span>
            <span className="text-slate-300 font-medium">{totalUserTabs}</span>
            {hiddenTabsCount > 0 && (
              <>
                <span className="text-slate-500">•</span>
                <span className="text-orange-400 font-medium">{hiddenTabsCount}</span>
              </>
            )}
          </div>
        </div>

        {/* 右侧：图标按钮组 */}
        <div className="flex items-center gap-1">
          {/* 继续隐藏按钮 - 纯图标 */}
          {canContinueHiding && (
            <button
              onClick={handleContinueHide}
              disabled={state.loading || visibleUserTabs === 0}
              className={`flex items-center justify-center w-7 h-7 rounded transition-all duration-200 ${
                state.loading || visibleUserTabs === 0
                  ? 'opacity-50 cursor-not-allowed bg-slate-700'
                  : 'bg-orange-600 hover:bg-orange-700 text-white'
              }`}
              title={`继续隐藏 ${visibleUserTabs} 个新的用户标签页`}
            >
              {state.loading ? (
                <Loader2 className="w-3.5 h-3.5 animate-spin" />
              ) : (
                <EyeOff className="w-3.5 h-3.5" />
              )}
              {/* 指示器 */}
              <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 rounded-full bg-yellow-400 animate-pulse" />
            </button>
          )}

          {/* 主要的隐藏/显示切换按钮 - 纯图标 */}
          <button
            onClick={handleToggleVisibility}
            disabled={state.loading || totalUserTabs === 0}
            className={`flex items-center justify-center w-7 h-7 rounded transition-all duration-200 ${
              state.loading || totalUserTabs === 0
                ? 'opacity-50 cursor-not-allowed bg-slate-700'
                : state.isHidden
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
            title={
              totalUserTabs === 0
                ? '没有用户标签页'
                : state.isHidden
                ? `显示 ${hiddenTabsCount} 个隐藏的用户标签页`
                : `隐藏 ${visibleUserTabs} 个用户标签页`
            }
          >
            {state.loading ? (
              <Loader2 className="w-3.5 h-3.5 animate-spin" />
            ) : state.isHidden ? (
              <Eye className="w-3.5 h-3.5" />
            ) : (
              <EyeOff className="w-3.5 h-3.5" />
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default GlobalUserTabsControl;
