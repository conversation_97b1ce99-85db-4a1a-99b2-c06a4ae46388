import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Loader2 } from 'lucide-react';

interface GlobalUserTabsState {
  isHidden: boolean;
  hiddenTabsCount: number;
  totalUserTabs: number;
  visibleUserTabs: number;
  canContinueHiding: boolean;
  actionType: 'hide' | 'continue_hide' | 'show';
  loading: boolean;
}

/**
 * 全局用户标签页控制组件
 */
const GlobalUserTabsControl: React.FC = () => {
  const [state, setState] = useState<GlobalUserTabsState>({
    isHidden: false,
    hiddenTabsCount: 0,
    totalUserTabs: 0,
    visibleUserTabs: 0,
    canContinueHiding: false,
    actionType: 'hide',
    loading: false,
  });

  /**
   * 加载全局用户标签页状态
   */
  const loadGlobalState = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 开发环境调试
      if (process.env.NODE_ENV === 'development') {
        try {
          const { GlobalTabsDebugger } = await import('@/utils/debugGlobalTabs');
          await GlobalTabsDebugger.cleanupInvalidHiddenTabs();
        } catch (error) {
          console.log('调试工具加载失败:', error);
        }
      }

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await GlobalUserTabsVisibilityManager.getGlobalUserTabsState();

      if (result.success) {
        const data = result.data!;
        setState({
          isHidden: data.isHidden,
          hiddenTabsCount: data.hiddenTabIds.length,
          totalUserTabs: data.totalUserTabs,
          visibleUserTabs: data.visibleUserTabs,
          canContinueHiding: data.canContinueHiding,
          actionType: data.actionType,
          loading: false,
        });

        // 开发环境输出调试信息
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 全局用户标签页状态更新:', {
            isHidden: data.isHidden,
            hiddenTabsCount: data.hiddenTabIds.length,
            totalUserTabs: data.totalUserTabs,
            visibleUserTabs: data.visibleUserTabs,
            canContinueHiding: data.canContinueHiding,
            actionType: data.actionType,
          });
        }
      } else {
        console.error('获取全局用户标签页状态失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('加载全局用户标签页状态时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 切换全局用户标签页显示状态
   */
  const handleToggleVisibility = async () => {
    try {
      setState(prev => ({ ...prev, loading: true }));

      // 动态导入全局用户标签页管理器
      const { GlobalUserTabsVisibilityManager } = await import('@/utils/tabs');
      const result = await GlobalUserTabsVisibilityManager.toggleGlobalUserTabsVisibility();

      if (result.success) {
        const { action, tabIds } = result.data!;
        let actionText = '';
        switch (action) {
          case 'hidden':
            actionText = '隐藏';
            break;
          case 'continue_hidden':
            actionText = '继续隐藏';
            break;
          case 'shown':
            actionText = '显示';
            break;
        }
        console.log(`✅ 全局用户标签页${actionText}成功，影响 ${tabIds.length} 个标签页`);

        // 重新加载状态
        await loadGlobalState();

        // 发送全局状态更新事件
        try {
          const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
          WorkspaceStateSync.sendWorkspaceStateUpdate('global', 'userTabsVisibility');
        } catch (error) {
          console.error('发送全局用户标签页状态变化事件失败:', error);
        }
      } else {
        console.error('切换全局用户标签页显示状态失败:', result.error);
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('切换全局用户标签页显示状态时出错:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  };

  /**
   * 监听全局状态变化事件
   */
  useEffect(() => {
    const setupStateListener = async () => {
      try {
        const { WorkspaceStateSync } = await import('@/utils/workspaceStateSync');
        
        const cleanup = WorkspaceStateSync.addStateListener((workspaceId, eventType) => {
          // 当有状态变化时，重新加载全局状态
          if (eventType === 'userTabsVisibility' || eventType === 'switch') {
            loadGlobalState();
          }
        });

        return cleanup;
      } catch (error) {
        console.error('设置全局状态监听器失败:', error);
        return () => {};
      }
    };

    let cleanup: (() => void) | undefined;
    
    setupStateListener().then(cleanupFn => {
      cleanup = cleanupFn;
    });

    // 初始加载状态
    loadGlobalState();

    // 清理事件监听器
    return () => {
      if (cleanup) {
        cleanup();
      }
    };
  }, []);

  // 使用从后端获取的准确数据
  const { visibleUserTabs, hiddenTabsCount, totalUserTabs, actionType, canContinueHiding } = state;

  // 根据操作类型确定按钮样式和文字
  const getButtonConfig = () => {
    switch (actionType) {
      case 'show':
        return {
          className: 'bg-green-600 hover:bg-green-700 text-white',
          icon: <Eye className="w-3 h-3" />,
          text: '显示',
          title: `显示 ${hiddenTabsCount} 个隐藏的用户标签页`
        };
      case 'continue_hide':
        return {
          className: 'bg-orange-600 hover:bg-orange-700 text-white',
          icon: <EyeOff className="w-3 h-3" />,
          text: '继续隐藏',
          title: `继续隐藏 ${visibleUserTabs} 个新的用户标签页`
        };
      case 'hide':
      default:
        return {
          className: 'bg-blue-600 hover:bg-blue-700 text-white',
          icon: <EyeOff className="w-3 h-3" />,
          text: '隐藏',
          title: `隐藏 ${visibleUserTabs} 个用户标签页`
        };
    }
  };

  const buttonConfig = getButtonConfig();

  return (
    <div className="px-3 py-2 border-b border-slate-700 bg-slate-800">
      {/* 单行紧凑布局 */}
      <div className="flex items-center justify-between">
        {/* 左侧：标题和状态 */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
            <span className="text-xs font-medium text-slate-300">
              用户标签页
            </span>
          </div>

          {/* 紧凑状态显示 */}
          <div className="flex items-center gap-2 text-xs">
            <span className="text-blue-400 font-medium">{visibleUserTabs}</span>
            <span className="text-slate-500">/</span>
            <span className="text-slate-300 font-medium">{totalUserTabs}</span>
            {hiddenTabsCount > 0 && (
              <>
                <span className="text-slate-500">•</span>
                <span className="text-orange-400 font-medium">{hiddenTabsCount}</span>
                <span className="text-slate-500 text-xs">隐藏</span>
              </>
            )}
          </div>
        </div>

        {/* 右侧：智能切换按钮 */}
        <button
          onClick={handleToggleVisibility}
          disabled={state.loading || totalUserTabs === 0}
          className={`flex items-center gap-1.5 px-2 py-1 rounded text-xs font-medium transition-all duration-200 ${
            state.loading || totalUserTabs === 0
              ? 'opacity-50 cursor-not-allowed bg-slate-700'
              : buttonConfig.className
          }`}
          title={
            totalUserTabs === 0
              ? '没有用户标签页'
              : buttonConfig.title
          }
        >
          {state.loading ? (
            <Loader2 className="w-3 h-3 animate-spin" />
          ) : (
            buttonConfig.icon
          )}

          <span className="hidden sm:inline">
            {state.loading ? '处理中' : buttonConfig.text}
          </span>

          {/* 继续隐藏模式的特殊指示器 */}
          {canContinueHiding && !state.loading && (
            <div className="w-1 h-1 rounded-full bg-yellow-400 animate-pulse" title="有新标签页可继续隐藏" />
          )}
        </button>
      </div>
    </div>
  );
};

export default GlobalUserTabsControl;
