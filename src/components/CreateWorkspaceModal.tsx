import React, { useState } from 'react';
import { X, Sparkles } from 'lucide-react';
import { WORKSPACE_COLORS, WORKSPACE_ICONS, WORKSPACE_TEMPLATES } from '@/utils/constants';

interface CreateWorkspaceModalProps {
  onClose: () => void;
  onCreate: (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
  }) => void;
}

/**
 * 创建工作区模态框
 */
const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  onClose,
  onCreate,
}) => {
  const [name, setName] = useState('');
  const [selectedIcon, setSelectedIcon] = useState<string>(WORKSPACE_ICONS[0]);
  const [selectedColor, setSelectedColor] = useState<string>(WORKSPACE_COLORS[0]);
  const [showTemplates, setShowTemplates] = useState(false);

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name.trim()) {
      return;
    }

    onCreate(name.trim(), {
      icon: selectedIcon,
      color: selectedColor,
    });
  };

  /**
   * 处理模板选择
   */
  const handleTemplateSelect = (template: typeof WORKSPACE_TEMPLATES[0]) => {
    setName(template.name);
    setSelectedIcon(template.icon);
    setSelectedColor(template.color);
    setShowTemplates(false);
  };

  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            创建新工作区
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>

        {/* 模板选择 */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-slate-200">
              快速开始
            </h3>
            <button
              onClick={() => setShowTemplates(!showTemplates)}
              className="text-xs text-blue-400 hover:text-blue-300"
            >
              {showTemplates ? '隐藏模板' : '显示模板'}
            </button>
          </div>

          {showTemplates && (
            <div className="grid grid-cols-1 gap-2 mb-4 max-h-32 overflow-y-auto">
              {WORKSPACE_TEMPLATES.map((template) => (
                <button
                  key={template.id}
                  onClick={() => handleTemplateSelect(template)}
                  className="flex items-center gap-3 p-3 bg-slate-700 hover:bg-slate-600 rounded-lg transition-colors duration-200 text-left"
                >
                  <span className="text-lg">{template.icon}</span>
                  <div>
                    <div className="text-sm font-medium text-white">
                      {template.name}
                    </div>
                    <div className="text-xs text-slate-400">
                      {template.description}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          {/* 工作区名称 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              工作区名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入工作区名称..."
              className="input-field w-full"
              autoFocus
            />
          </div>

          {/* 图标选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择图标
            </label>
            <div className="grid grid-cols-8 gap-2 max-h-24 overflow-y-auto">
              {WORKSPACE_ICONS.map((icon) => (
                <button
                  key={icon}
                  type="button"
                  onClick={() => setSelectedIcon(icon)}
                  className={`p-2 rounded-lg text-lg transition-colors duration-200 ${
                    selectedIcon === icon
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
          </div>

          {/* 颜色选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择颜色
            </label>
            <div className="flex gap-2 flex-wrap">
              {WORKSPACE_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-8 rounded-lg transition-all duration-200 ${
                    selectedColor === color
                      ? 'ring-2 ring-white ring-offset-2 ring-offset-slate-800'
                      : 'hover:scale-110'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* 预览 */}
          <div className="mb-4 p-3 bg-slate-700 rounded-lg">
            <div className="text-sm text-slate-200 mb-2">预览</div>
            <div className="flex items-center gap-3">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
                style={{ backgroundColor: selectedColor + '20', color: selectedColor }}
              >
                {selectedIcon}
              </div>
              <span className="text-white font-medium">
                {name || '工作区名称'}
              </span>
            </div>
          </div>



          {/* 按钮 */}
          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!name.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Sparkles className="w-4 h-4" />
              创建工作区
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateWorkspaceModal;
