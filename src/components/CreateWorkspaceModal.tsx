import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>rk<PERSON>, Download, <PERSON>, EyeOff } from 'lucide-react';
import { WORKSPACE_COLORS, WORKSPACE_ICONS } from '@/utils/constants';
import { TabExtractor, ExtractableTab } from '@/utils/tabExtractor';
import { Website } from '@/types/workspace';

interface CreateWorkspaceModalProps {
  onClose: () => void;
  onCreate: (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
    websites?: Website[];
  }) => void;
}

/**
 * 创建工作区模态框
 */
const CreateWorkspaceModal: React.FC<CreateWorkspaceModalProps> = ({
  onClose,
  onCreate,
}) => {
  const [name, setName] = useState('');
  const [selectedIcon, setSelectedIcon] = useState<string>(WORKSPACE_ICONS[0]);
  const [selectedColor, setSelectedColor] = useState<string>(WORKSPACE_COLORS[0]);

  // 标签页保存相关状态
  const [showTabExtraction, setShowTabExtraction] = useState(false);
  const [extractableTabs, setExtractableTabs] = useState<ExtractableTab[]>([]);
  const [selectedTabIds, setSelectedTabIds] = useState<Set<number>>(new Set());
  const [tabStats, setTabStats] = useState<{
    total: number;
    extractable: number;
    pinned: number;
    system: number;
    blank: number;
  } | null>(null);
  const [isLoadingTabs, setIsLoadingTabs] = useState(false);

  /**
   * 加载标签页统计信息
   */
  const loadTabStats = async () => {
    try {
      const statsResult = await TabExtractor.getTabStatistics();
      if (statsResult.success) {
        setTabStats(statsResult.data!);
      }
    } catch (error) {
      console.error('加载标签页统计失败:', error);
    }
  };

  /**
   * 处理保存当前标签页
   */
  const handleSaveCurrentTabs = async () => {
    try {
      setIsLoadingTabs(true);
      console.log('🔍 开始提取当前标签页');

      const tabsResult = await TabExtractor.getCurrentWindowTabs({
        includeSystemPages: false,
        includePinnedTabs: true,
        includeBlankTabs: false
      });

      if (!tabsResult.success) {
        console.error('提取标签页失败:', tabsResult.error);
        return;
      }

      const tabs = tabsResult.data!;
      setExtractableTabs(tabs);

      // 默认选中所有标签页
      const allTabIds = new Set(tabs.map(tab => tab.id));
      setSelectedTabIds(allTabIds);

      // 如果没有工作区名称，使用默认名称
      if (!name.trim()) {
        const timestamp = new Date().toLocaleString('zh-CN', {
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        });
        setName(`标签页工作区 ${timestamp}`);
      }

      setShowTabExtraction(true);
      console.log(`✅ 提取到 ${tabs.length} 个可用标签页`);

    } catch (error) {
      console.error('保存当前标签页失败:', error);
    } finally {
      setIsLoadingTabs(false);
    }
  };

  /**
   * 处理标签页选择切换
   */
  const handleToggleTab = (tabId: number) => {
    const newSelectedIds = new Set(selectedTabIds);
    if (newSelectedIds.has(tabId)) {
      newSelectedIds.delete(tabId);
    } else {
      newSelectedIds.add(tabId);
    }
    setSelectedTabIds(newSelectedIds);
  };

  /**
   * 处理全选/取消全选
   */
  const handleToggleAll = () => {
    if (selectedTabIds.size === extractableTabs.length) {
      setSelectedTabIds(new Set());
    } else {
      const allTabIds = new Set(extractableTabs.map(tab => tab.id));
      setSelectedTabIds(allTabIds);
    }
  };

  /**
   * 处理表单提交
   */
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      return;
    }

    let websites: Website[] | undefined;

    // 如果选择了标签页，转换为网站列表
    if (showTabExtraction && selectedTabIds.size > 0) {
      websites = TabExtractor.convertTabsToWebsites(extractableTabs, selectedTabIds);
      console.log(`🔄 转换 ${websites.length} 个标签页为网站列表`);
    }

    onCreate(name.trim(), {
      icon: selectedIcon,
      color: selectedColor,
      websites
    });
  };



  /**
   * 处理ESC键关闭
   */
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onKeyDown={handleKeyDown}>
      <div className="modal-content animate-fade-in">
        {/* 头部 */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-white">
            创建新工作区
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-700 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-slate-400" />
          </button>
        </div>



        {/* 表单 */}
        <form onSubmit={handleSubmit}>
          {/* 工作区名称 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              工作区名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入工作区名称..."
              className="input-field w-full"
              autoFocus
            />
          </div>

          {/* 图标选择 - 优化360px宽度 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择图标
            </label>
            <div className="grid grid-cols-6 gap-1.5 max-h-20 overflow-y-auto">
              {WORKSPACE_ICONS.map((icon) => (
                <button
                  key={icon}
                  type="button"
                  onClick={() => setSelectedIcon(icon)}
                  className={`p-1.5 rounded text-base transition-colors duration-200 ${
                    selectedIcon === icon
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
          </div>

          {/* 颜色选择 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-slate-200 mb-2">
              选择颜色
            </label>
            <div className="flex gap-2 flex-wrap">
              {WORKSPACE_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => setSelectedColor(color)}
                  className={`w-8 h-8 rounded-lg transition-all duration-200 ${
                    selectedColor === color
                      ? 'ring-2 ring-white ring-offset-2 ring-offset-slate-800'
                      : 'hover:scale-110'
                  }`}
                  style={{ backgroundColor: color }}
                />
              ))}
            </div>
          </div>

          {/* 预览 */}
          <div className="mb-4 p-3 bg-slate-700 rounded-lg">
            <div className="text-sm text-slate-200 mb-2">预览</div>
            <div className="flex items-center gap-3">
              <div 
                className="w-8 h-8 rounded-lg flex items-center justify-center text-lg"
                style={{ backgroundColor: selectedColor + '20', color: selectedColor }}
              >
                {selectedIcon}
              </div>
              <span className="text-white font-medium">
                {name || '工作区名称'}
              </span>
            </div>
          </div>



          {/* 按钮 */}
          <div className="flex gap-3 justify-end">
            <button
              type="button"
              onClick={onClose}
              className="btn-secondary"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={!name.trim()}
              className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Sparkles className="w-4 h-4" />
              创建工作区
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateWorkspaceModal;
