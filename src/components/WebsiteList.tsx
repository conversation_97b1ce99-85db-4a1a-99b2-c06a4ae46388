import React, { useState, useEffect } from 'react';
import { ExternalLink, <PERSON>, Pin, PinOff, Edit, Check, Trash2 } from 'lucide-react';
import { Website } from '@/types/workspace';

interface WebsiteListProps {
  websites: Website[];
  onRemoveWebsite: (websiteId: string) => void;
  onEditWebsite: (website: Website) => void;
  onReorderWebsites?: (websiteIds: string[]) => void;
  onTogglePin?: (websiteId: string, isPinned: boolean) => void;
  onBatchPin?: (websiteIds: string[], isPinned: boolean) => void;
  onBatchDelete?: (websiteIds: string[]) => void;
}

/**
 * 网站列表组件
 */
const WebsiteList: React.FC<WebsiteListProps> = ({
  websites,
  onRemoveWebsite,
  onEditWebsite,
  onReorderWebsites: _onReorderWebsites,
  onTogglePin,
  onBatchPin,
  onBatchDelete,
}) => {
  const [selectedWebsites, setSelectedWebsites] = useState<Set<string>>(new Set());
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [tabStates, setTabStates] = useState<Record<string, { isPinned: boolean; exists: boolean }>>({});

  // 获取当前标签页状态
  useEffect(() => {
    const updateTabStates = async () => {
      const states: Record<string, { isPinned: boolean; exists: boolean }> = {};

      for (const website of websites) {
        try {
          const tabs = await chrome.tabs.query({ url: website.url + '*' });
          if (tabs.length > 0) {
            states[website.id] = {
              isPinned: tabs[0].pinned,
              exists: true
            };
          } else {
            states[website.id] = {
              isPinned: false,
              exists: false
            };
          }
        } catch (error) {
          console.error('Failed to query tab state:', error);
          states[website.id] = {
            isPinned: false,
            exists: false
          };
        }
      }

      setTabStates(states);
    };

    updateTabStates();

    // 定期更新标签页状态 - 提高响应速度到1秒
    const interval = setInterval(updateTabStates, 1000);
    return () => clearInterval(interval);
  }, [websites]);
  /**
   * 处理网站点击
   */
  const handleWebsiteClick = async (website: Website) => {
    try {
      // 检查是否已经打开
      const tabs = await chrome.tabs.query({ url: website.url });
      
      if (tabs.length > 0) {
        // 激活已存在的标签页
        await chrome.tabs.update(tabs[0].id!, { active: true });
      } else {
        // 创建新标签页
        await chrome.tabs.create({ 
          url: website.url,
          pinned: website.isPinned 
        });
      }
    } catch (error) {
      console.error('Failed to open website:', error);
    }
  };

  /**
   * 处理移除网站
   */
  const handleRemoveWebsite = (e: React.MouseEvent, websiteId: string) => {
    e.stopPropagation();
    onRemoveWebsite(websiteId);
  };

  /**
   * 处理编辑网站
   */
  const handleEditWebsite = (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();
    onEditWebsite(website);
  };

  /**
   * 处理固定状态切换
   */
  const handleTogglePin = async (e: React.MouseEvent, website: Website) => {
    e.stopPropagation();

    const currentState = tabStates[website.id];
    const newPinnedState = !currentState?.isPinned;

    try {
      // 查找对应的标签页
      const tabs = await chrome.tabs.query({ url: website.url + '*' });

      if (tabs.length > 0) {
        // 更新标签页的固定状态
        await chrome.tabs.update(tabs[0].id!, { pinned: newPinnedState });

        // 立即更新本地状态
        setTabStates(prev => ({
          ...prev,
          [website.id]: {
            ...prev[website.id],
            isPinned: newPinnedState
          }
        }));
      }

      // 通知父组件更新
      if (onTogglePin) {
        onTogglePin(website.id, newPinnedState);
      }
    } catch (error) {
      console.error('Failed to toggle pin state:', error);
    }
  };

  /**
   * 处理选择模式切换
   */
  const handleToggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedWebsites(new Set());
  };

  /**
   * 处理网站选择
   */
  const handleWebsiteSelect = (websiteId: string) => {
    const newSelected = new Set(selectedWebsites);
    if (newSelected.has(websiteId)) {
      newSelected.delete(websiteId);
    } else {
      newSelected.add(websiteId);
    }
    setSelectedWebsites(newSelected);
  };

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = () => {
    if (selectedWebsites.size === websites.length) {
      setSelectedWebsites(new Set());
    } else {
      setSelectedWebsites(new Set(websites.map(w => w.id)));
    }
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = async (isPinned: boolean) => {
    const selectedIds = Array.from(selectedWebsites);

    try {
      // 更新所有选中网站对应的标签页
      for (const websiteId of selectedIds) {
        const website = websites.find(w => w.id === websiteId);
        if (website) {
          const tabs = await chrome.tabs.query({ url: website.url + '*' });
          if (tabs.length > 0) {
            await chrome.tabs.update(tabs[0].id!, { pinned: isPinned });
          }
        }
      }

      // 更新本地状态
      setTabStates(prev => {
        const newStates = { ...prev };
        selectedIds.forEach(id => {
          if (newStates[id]) {
            newStates[id] = { ...newStates[id], isPinned };
          }
        });
        return newStates;
      });

      // 通知父组件
      if (onBatchPin) {
        onBatchPin(selectedIds, isPinned);
      }

      // 退出选择模式
      setIsSelectionMode(false);
      setSelectedWebsites(new Set());
    } catch (error) {
      console.error('Failed to batch pin websites:', error);
    }
  };

  /**
   * 处理批量删除
   */
  const handleBatchDelete = () => {
    const selectedIds = Array.from(selectedWebsites);

    if (onBatchDelete) {
      onBatchDelete(selectedIds);
    }

    // 退出选择模式
    setIsSelectionMode(false);
    setSelectedWebsites(new Set());
  };

  /**
   * 获取网站图标
   */
  const getWebsiteIcon = (website: Website) => {
    if (website.favicon && website.favicon !== '') {
      return (
        <img
          src={website.favicon}
          alt=""
          className="w-4 h-4 rounded"
          onError={(e) => {
            // 如果图标加载失败，显示默认图标
            (e.target as HTMLImageElement).style.display = 'none';
          }}
        />
      );
    }
    
    // 默认图标
    return (
      <div className="w-4 h-4 bg-slate-600 rounded flex items-center justify-center">
        <ExternalLink className="w-2.5 h-2.5 text-slate-400" />
      </div>
    );
  };

  /**
   * 格式化URL显示
   */
  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // 按order字段排序
  const sortedWebsites = [...websites].sort((a, b) => a.order - b.order);

  return (
    <div className="space-y-1">
      {/* 批量操作工具栏 - 充满布局 */}
      {websites.length > 0 && (
        <div className="mb-2">
          {/* 主操作栏 - 消除左侧间距 */}
          <div className="flex items-center justify-between px-1 py-1.5 bg-slate-700/80 rounded border border-slate-600/50">
            <div className="flex items-center gap-2">
              <button
                onClick={handleToggleSelectionMode}
                className={`px-2 py-1 rounded text-xs font-medium transition-all duration-200 ${
                  isSelectionMode
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white'
                }`}
              >
                {isSelectionMode ? '退出' : '批量'}
              </button>

              {isSelectionMode && (
                <>
                  <button
                    onClick={handleSelectAll}
                    className="px-2 py-1 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded text-xs transition-all duration-200"
                  >
                    {selectedWebsites.size === websites.length ? '取消' : '全选'}
                  </button>

                  <div className="flex items-center gap-1 px-1.5 py-1 bg-slate-800/50 rounded">
                    <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
                    <span className="text-xs text-slate-300">
                      {selectedWebsites.size}
                    </span>
                  </div>
                </>
              )}
            </div>

            {/* 批量操作按钮 - 纯图标 */}
            {isSelectionMode && selectedWebsites.size > 0 && (
              <div className="flex items-center gap-1">
                <button
                  onClick={() => handleBatchPin(true)}
                  className="flex items-center justify-center w-6 h-6 bg-blue-600 text-white hover:bg-blue-700 rounded transition-all duration-200"
                  title="全部固定"
                >
                  <Pin className="w-3 h-3" />
                </button>
                <button
                  onClick={() => handleBatchPin(false)}
                  className="flex items-center justify-center w-6 h-6 bg-slate-600 text-slate-200 hover:bg-slate-500 hover:text-white rounded transition-all duration-200"
                  title="取消固定"
                >
                  <PinOff className="w-3 h-3" />
                </button>
                <button
                  onClick={handleBatchDelete}
                  className="flex items-center justify-center w-6 h-6 bg-red-600 text-white hover:bg-red-700 rounded transition-all duration-200"
                  title="删除选中"
                >
                  <Trash2 className="w-3 h-3" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {sortedWebsites.map((website) => {
        const tabState = tabStates[website.id];
        const isSelected = selectedWebsites.has(website.id);

        return (
        <div
          key={website.id}
          className={`website-item ${isSelected ? 'bg-blue-600/20 border-blue-500' : ''}`}
          onClick={() => isSelectionMode ? handleWebsiteSelect(website.id) : handleWebsiteClick(website)}
        >
          {/* 选择框 */}
          {isSelectionMode && (
            <div className="flex-shrink-0">
              <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                isSelected
                  ? 'bg-blue-600 border-blue-600'
                  : 'border-slate-400'
              }`}>
                {isSelected && <Check className="w-3 h-3 text-white" />}
              </div>
            </div>
          )}

          {/* 网站图标 */}
          <div className="flex-shrink-0">
            {getWebsiteIcon(website)}
          </div>

          {/* 网站信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-1">
              <span className="text-sm text-white truncate">
                {website.title}
              </span>
              {/* 显示实际标签页的固定状态 */}
              {tabState?.exists && tabState.isPinned && (
                <Pin className="w-3 h-3 text-blue-400 flex-shrink-0" title="标签页已固定" />
              )}
              {tabState?.exists && !tabState.isPinned && (
                <PinOff className="w-3 h-3 text-slate-400 flex-shrink-0" title="标签页未固定" />
              )}
            </div>
            <p className="text-xs text-slate-400 truncate">
              {formatUrl(website.url)}
              {tabState?.exists && (
                <span className="ml-2 text-green-400">• 已打开</span>
              )}
            </p>
          </div>

          {/* 操作按钮 */}
          {!isSelectionMode && (
            <div className="website-actions flex items-center gap-1">
              {/* 固定状态切换按钮 */}
              {tabState?.exists && (
                <button
                  onClick={(e) => handleTogglePin(e, website)}
                  className={`p-1 rounded transition-colors duration-150 ${
                    tabState.isPinned
                      ? 'hover:bg-slate-500 text-blue-400'
                      : 'hover:bg-blue-600 text-slate-400'
                  }`}
                  title={tabState.isPinned ? '取消固定' : '固定标签页'}
                >
                  {tabState.isPinned ? (
                    <PinOff className="w-3 h-3" />
                  ) : (
                    <Pin className="w-3 h-3" />
                  )}
                </button>
              )}

              <button
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(website.url, '_blank');
                }}
                className="p-1 hover:bg-slate-500 rounded transition-colors duration-150"
                title="在新标签页中打开"
              >
                <ExternalLink className="w-3 h-3 text-slate-400" />
              </button>
              <button
                onClick={(e) => handleEditWebsite(e, website)}
                className="p-1 hover:bg-blue-600 rounded transition-colors duration-150"
                title="编辑网站"
              >
                <Edit className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
              <button
                onClick={(e) => handleRemoveWebsite(e, website.id)}
                className="p-1 hover:bg-red-600 rounded transition-colors duration-150"
                title="移除网站"
              >
                <X className="w-3 h-3 text-slate-400 hover:text-white" />
              </button>
            </div>
          )}
        </div>
        );
      })}
    </div>
  );
};

export default WebsiteList;
