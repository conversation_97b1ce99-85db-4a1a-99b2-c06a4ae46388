/**
 * 标签页提取和过滤工具
 * 用于将当前标签页转换为工作区网站列表
 */

import { Website } from '@/types/workspace';
import { OperationResult } from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 可提取的标签页信息
 */
export interface ExtractableTab {
  id: number;
  url: string;
  title: string;
  pinned: boolean;
  favIconUrl?: string;
  selected: boolean; // 用户是否选择保存此标签页
}

/**
 * 标签页提取选项
 */
export interface TabExtractionOptions {
  includeSystemPages?: boolean; // 是否包含系统页面
  includePinnedTabs?: boolean;  // 是否包含固定标签页
  includeBlankTabs?: boolean;   // 是否包含空白标签页
}

/**
 * 标签页提取器类
 */
export class TabExtractor {
  /**
   * 系统页面协议列表
   */
  private static readonly SYSTEM_PROTOCOLS = [
    'chrome://',
    'chrome-extension://',
    'about:',
    'data:',
    'javascript:',
    'moz-extension://',
    'edge://',
    'opera://',
    'vivaldi://',
    'brave://'
  ];

  /**
   * 检查URL是否为系统页面
   */
  private static isSystemPage(url: string): boolean {
    if (!url) return true;
    
    return this.SYSTEM_PROTOCOLS.some(protocol => 
      url.toLowerCase().startsWith(protocol.toLowerCase())
    );
  }

  /**
   * 检查URL是否为空白页面
   */
  private static isBlankPage(url: string): boolean {
    if (!url) return true;
    
    const blankUrls = [
      'about:blank',
      'chrome://newtab/',
      'edge://newtab/',
      'opera://startpage',
      'vivaldi://startpage'
    ];
    
    return blankUrls.some(blankUrl => 
      url.toLowerCase() === blankUrl.toLowerCase()
    );
  }

  /**
   * 标准化网站标题
   */
  private static normalizeTitle(title: string, url: string): string {
    if (!title || title.trim() === '') {
      try {
        const urlObj = new URL(url);
        return urlObj.hostname;
      } catch {
        return url;
      }
    }
    
    // 移除常见的标题后缀
    const suffixes = [' - Google Chrome', ' - Microsoft Edge', ' - Firefox', ' - Safari'];
    let normalizedTitle = title;
    
    for (const suffix of suffixes) {
      if (normalizedTitle.endsWith(suffix)) {
        normalizedTitle = normalizedTitle.slice(0, -suffix.length);
        break;
      }
    }
    
    return normalizedTitle.trim();
  }

  /**
   * 获取当前窗口的所有可提取标签页
   */
  static async getCurrentWindowTabs(
    options: TabExtractionOptions = {}
  ): Promise<OperationResult<ExtractableTab[]>> {
    try {
      console.log('🔍 开始提取当前窗口标签页');
      
      // 获取当前窗口的所有标签页
      const tabs = await chrome.tabs.query({ currentWindow: true });
      console.log(`📊 找到 ${tabs.length} 个标签页`);
      
      const extractableTabs: ExtractableTab[] = [];
      
      for (const tab of tabs) {
        if (!tab.url || !tab.id) continue;
        
        // 应用过滤规则
        const isSystem = this.isSystemPage(tab.url);
        const isBlank = this.isBlankPage(tab.url);
        const isPinned = tab.pinned;
        
        // 根据选项决定是否包含
        if (isSystem && !options.includeSystemPages) {
          console.log('⏭️ 跳过系统页面:', tab.url);
          continue;
        }
        
        if (isBlank && !options.includeBlankTabs) {
          console.log('⏭️ 跳过空白页面:', tab.url);
          continue;
        }
        
        if (isPinned && !options.includePinnedTabs) {
          console.log('⏭️ 跳过固定标签页:', tab.url);
          continue;
        }
        
        const extractableTab: ExtractableTab = {
          id: tab.id,
          url: tab.url,
          title: this.normalizeTitle(tab.title || '', tab.url),
          pinned: tab.pinned,
          favIconUrl: tab.favIconUrl,
          selected: true // 默认选中
        };
        
        extractableTabs.push(extractableTab);
        console.log('✅ 添加可提取标签页:', extractableTab.title, extractableTab.url);
      }
      
      console.log(`📋 共提取 ${extractableTabs.length} 个可用标签页`);
      
      return {
        success: true,
        data: extractableTabs
      };
      
    } catch (error) {
      console.error('提取标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to extract current window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 将可提取标签页转换为网站列表
   */
  static convertTabsToWebsites(
    extractableTabs: ExtractableTab[],
    selectedTabIds?: Set<number>
  ): Website[] {
    const websites: Website[] = [];
    
    for (const tab of extractableTabs) {
      // 如果指定了选择的标签页ID，只转换选中的
      if (selectedTabIds && !selectedTabIds.has(tab.id)) {
        continue;
      }
      
      // 如果没有指定选择列表，使用标签页自身的selected状态
      if (!selectedTabIds && !tab.selected) {
        continue;
      }
      
      const website: Website = {
        id: `tab-${tab.id}-${Date.now()}`, // 生成唯一ID
        title: tab.title,
        url: tab.url,
        isPinned: tab.pinned,
        favicon: tab.favIconUrl
      };
      
      websites.push(website);
    }
    
    console.log(`🔄 转换 ${websites.length} 个标签页为网站列表`);
    return websites;
  }

  /**
   * 一键提取当前标签页并转换为网站列表
   */
  static async extractCurrentTabsAsWebsites(
    options: TabExtractionOptions = {}
  ): Promise<OperationResult<Website[]>> {
    try {
      // 设置默认选项：排除系统页面和空白页面，包含固定标签页
      const defaultOptions: TabExtractionOptions = {
        includeSystemPages: false,
        includePinnedTabs: true,
        includeBlankTabs: false,
        ...options
      };
      
      const tabsResult = await this.getCurrentWindowTabs(defaultOptions);
      if (!tabsResult.success) {
        return { success: false, error: tabsResult.error };
      }
      
      const websites = this.convertTabsToWebsites(tabsResult.data!);
      
      return {
        success: true,
        data: websites
      };
      
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to extract current tabs as websites',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签页统计信息
   */
  static async getTabStatistics(): Promise<OperationResult<{
    total: number;
    pinned: number;
    system: number;
    blank: number;
    extractable: number;
  }>> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      
      let pinned = 0;
      let system = 0;
      let blank = 0;
      let extractable = 0;
      
      for (const tab of tabs) {
        if (!tab.url) continue;
        
        if (tab.pinned) pinned++;
        if (this.isSystemPage(tab.url)) system++;
        if (this.isBlankPage(tab.url)) blank++;
        
        // 可提取的标签页：非系统页面且非空白页面
        if (!this.isSystemPage(tab.url) && !this.isBlankPage(tab.url)) {
          extractable++;
        }
      }
      
      return {
        success: true,
        data: {
          total: tabs.length,
          pinned,
          system,
          blank,
          extractable
        }
      };
      
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tab statistics',
          details: error,
        },
      };
    }
  }
}
