/**
 * 全局用户标签页调试工具
 * 用于诊断计数和状态问题
 */

export class GlobalTabsDebugger {
  /**
   * 打印详细的标签页状态信息
   */
  static async debugTabsState(): Promise<void> {
    try {
      console.group('🔍 全局用户标签页状态调试');

      // 1. 获取存储状态
      const storageResult = await chrome.storage.local.get(['globalUserTabsHidden', 'globalHiddenTabIds']);
      console.log('📦 存储状态:', storageResult);

      // 2. 获取所有标签页
      const allTabs = await chrome.tabs.query({});
      console.log('📋 所有标签页总数:', allTabs.length);

      // 3. 获取当前窗口
      const currentWindow = await chrome.windows.getCurrent();
      console.log('🪟 当前窗口ID:', currentWindow.id);

      // 4. 获取工作区管理的网站信息
      let workspaceWebsites: Array<{url: string; isPinned: boolean; workspaceId: string}> = [];
      try {
        const { StorageManager } = await import('./storage');
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success) {
          const workspaces = workspacesResult.data!;
          workspaceWebsites = workspaces.flatMap(workspace =>
            workspace.websites.map(website => ({
              url: website.url,
              isPinned: website.isPinned,
              workspaceId: workspace.id
            }))
          );
        }
      } catch (error) {
        console.warn('获取工作区网站信息失败:', error);
      }

      console.log('🏢 工作区管理的网站:', workspaceWebsites);

      // 5. 分类标签页
      const categorizedTabs = {
        all: allTabs,
        currentWindow: allTabs.filter(tab => tab.windowId === currentWindow.id),
        placeholder: allTabs.filter(tab => tab.url?.includes('workspace-placeholder.html')),
        extension: allTabs.filter(tab => tab.url?.includes('chrome-extension://')),
        chrome: allTabs.filter(tab => tab.url?.includes('chrome://')),
        pinned: allTabs.filter(tab => tab.pinned),
        workspaceManaged: allTabs.filter(tab => {
          return workspaceWebsites.some(website => {
            if (!tab.url?.startsWith(website.url)) return false;
            // 如果URL匹配且固定状态也匹配，认为是工作区管理的
            return tab.pinned === website.isPinned;
          });
        }),
        realUser: allTabs.filter(tab => {
          // 排除系统页面
          if (tab.url?.includes('workspace-placeholder.html') ||
              tab.url?.includes('chrome-extension://') ||
              tab.url?.includes('chrome://')) {
            return false;
          }

          // 检查是否为工作区管理的标签页
          const isWorkspaceManaged = workspaceWebsites.some(website => {
            if (!tab.url?.startsWith(website.url)) return false;
            return tab.pinned === website.isPinned;
          });

          return !isWorkspaceManaged;
        }),
        realUserInCurrentWindow: allTabs.filter(tab => {
          if (tab.windowId !== currentWindow.id) return false;

          // 排除系统页面
          if (tab.url?.includes('workspace-placeholder.html') ||
              tab.url?.includes('chrome-extension://') ||
              tab.url?.includes('chrome://')) {
            return false;
          }

          // 检查是否为工作区管理的标签页
          const isWorkspaceManaged = workspaceWebsites.some(website => {
            if (!tab.url?.startsWith(website.url)) return false;
            return tab.pinned === website.isPinned;
          });

          return !isWorkspaceManaged;
        }),
      };

      console.table({
        '所有标签页': categorizedTabs.all.length,
        '当前窗口标签页': categorizedTabs.currentWindow.length,
        '占位符页面': categorizedTabs.placeholder.length,
        '扩展页面': categorizedTabs.extension.length,
        'Chrome页面': categorizedTabs.chrome.length,
        '固定标签页': categorizedTabs.pinned.length,
        '工作区管理的标签页': categorizedTabs.workspaceManaged.length,
        '真正的用户标签页(全部)': categorizedTabs.realUser.length,
        '真正的用户标签页(当前窗口)': categorizedTabs.realUserInCurrentWindow.length,
      });

      // 5. 检查隐藏的标签页
      const hiddenTabIds = storageResult.globalHiddenTabIds || [];
      if (hiddenTabIds.length > 0) {
        console.log('🔒 隐藏的标签页ID:', hiddenTabIds);
        
        const hiddenTabsInfo = [];
        for (const tabId of hiddenTabIds) {
          try {
            const tab = await chrome.tabs.get(tabId);
            hiddenTabsInfo.push({
              id: tab.id,
              title: tab.title,
              url: tab.url,
              windowId: tab.windowId,
              exists: true
            });
          } catch {
            hiddenTabsInfo.push({
              id: tabId,
              exists: false
            });
          }
        }
        console.table(hiddenTabsInfo);
      }

      // 6. 显示工作区管理的标签页详情
      if (categorizedTabs.workspaceManaged.length > 0) {
        console.log('🏢 工作区管理的标签页详情:');
        console.table(categorizedTabs.workspaceManaged.map(tab => ({
          id: tab.id,
          title: tab.title?.substring(0, 30) + '...',
          url: tab.url?.substring(0, 50) + '...',
          windowId: tab.windowId,
          pinned: tab.pinned
        })));
      }

      // 7. 显示真正的用户标签页详情
      if (categorizedTabs.realUserInCurrentWindow.length > 0) {
        console.log('👤 真正的用户标签页详情(当前窗口):');
        console.table(categorizedTabs.realUserInCurrentWindow.map(tab => ({
          id: tab.id,
          title: tab.title?.substring(0, 30) + '...',
          url: tab.url?.substring(0, 50) + '...',
          windowId: tab.windowId,
          pinned: tab.pinned
        })));
      }

      // 8. 获取所有窗口信息
      const allWindows = await chrome.windows.getAll();
      console.log('🪟 所有窗口信息:');
      console.table(allWindows.map(window => ({
        id: window.id,
        type: window.type,
        state: window.state,
        focused: window.focused,
        tabsCount: allTabs.filter(tab => tab.windowId === window.id).length
      })));

      console.groupEnd();
    } catch (error) {
      console.error('❌ 调试过程中出错:', error);
    }
  }

  /**
   * 清理无效的隐藏标签页ID
   */
  static async cleanupInvalidHiddenTabs(): Promise<void> {
    try {
      const result = await chrome.storage.local.get(['globalHiddenTabIds']);
      const hiddenTabIds = result.globalHiddenTabIds || [];
      
      if (hiddenTabIds.length === 0) {
        console.log('✅ 没有隐藏的标签页需要清理');
        return;
      }

      const validTabIds = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validTabIds.push(tabId);
        } catch {
          console.log(`🗑️ 清理无效的标签页ID: ${tabId}`);
        }
      }

      if (validTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          globalHiddenTabIds: validTabIds
        });
        
        if (validTabIds.length === 0) {
          await chrome.storage.local.set({
            globalUserTabsHidden: false
          });
        }
        
        console.log(`✅ 清理完成，有效隐藏标签页: ${validTabIds.length}/${hiddenTabIds.length}`);
      } else {
        console.log('✅ 所有隐藏的标签页都有效');
      }
    } catch (error) {
      console.error('❌ 清理过程中出错:', error);
    }
  }

  /**
   * 重置全局状态
   */
  static async resetGlobalState(): Promise<void> {
    try {
      await chrome.storage.local.remove(['globalUserTabsHidden', 'globalHiddenTabIds']);
      console.log('✅ 全局状态已重置');
    } catch (error) {
      console.error('❌ 重置状态失败:', error);
    }
  }
}

// 在开发环境中暴露到全局对象
if (typeof window !== 'undefined') {
  (window as any).GlobalTabsDebugger = GlobalTabsDebugger;
}
