import {
  WorkSpace,
  Website,
  CreateWorkspaceOptions,
  UpdateWorkspaceOptions,
  AddWebsiteOptions,
  OperationResult
} from '@/types/workspace';
import { StorageManager } from './storage';
import { WindowManager } from './windowManager';
import {
  ERROR_CODES,
  WORKSPACE_COLORS,
  WORKSPACE_ICONS,
  URL_REGEX,
  DEFAULT_FAVICON
} from './constants';

/**
 * 工作区管理类
 */
export class WorkspaceManager {
  /**
   * 生成唯一ID
   */
  private static generateId(): string {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 生成网站ID
   */
  private static generateWebsiteId(): string {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 验证URL格式
   */
  private static isValidUrl(url: string): boolean {
    return URL_REGEX.test(url);
  }

  /**
   * 获取网站favicon
   */
  private static async getFavicon(url: string): Promise<string> {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }

  /**
   * 获取网站标题
   */
  private static async getWebsiteTitle(url: string): Promise<string> {
    try {
      // 尝试从当前标签页获取标题
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      
      // 从URL提取域名作为标题
      const domain = new URL(url).hostname;
      return domain.replace('www.', '');
    } catch {
      return url;
    }
  }

  /**
   * 创建新工作区
   */
  static async createWorkspace(options: CreateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    try {
      // 验证工作区名称
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Workspace name cannot be empty',
          },
        };
      }

      // 获取现有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }

      const existingWorkspaces = workspacesResult.data!;

      // 检查重复名称
      if (existingWorkspaces.some(w => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: 'Workspace with this name already exists',
          },
        };
      }

      // 创建工作区
      const workspace: WorkSpace = {
        id: this.generateId(),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length,
      };

      // 添加网站
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website: Website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            isPinned: siteData.isPinned,
            addedAt: Date.now(),
            order: i,
          };
          workspace.websites.push(website);
        }
      }

      // 保存工作区
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }

      // 如果需要激活
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }

      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to create workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区
   */
  static async updateWorkspace(id: string, options: UpdateWorkspaceOptions): Promise<OperationResult<WorkSpace>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`,
          },
        };
      }

      const workspace = workspaces[workspaceIndex];

      // 更新字段
      if (options.name !== undefined) workspace.name = options.name;
      if (options.icon !== undefined) workspace.icon = options.icon;
      if (options.color !== undefined) workspace.color = options.color;
      if (options.websites !== undefined) workspace.websites = options.websites;
      if (options.isActive !== undefined) workspace.isActive = options.isActive;
      
      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 删除工作区
   */
  static async deleteWorkspace(id: string): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspaceIndex = workspaces.findIndex(w => w.id === id);

      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`,
          },
        };
      }

      // 关闭工作区的专用窗口
      const closeWindowResult = await WindowManager.closeWorkspaceWindow(id);
      if (!closeWindowResult.success) {
        // 继续执行，不要因为窗口关闭失败而阻止工作区删除
      }

      // 移除工作区
      workspaces.splice(workspaceIndex, 1);

      // 重新排序
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      // 如果删除的是活跃工作区，清除活跃状态
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to delete workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId: string, url: string, options: AddWebsiteOptions = {}): Promise<OperationResult<Website>> {
    try {
      // 验证URL
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: 'Invalid URL format',
          },
        };
      }

      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      // 检查重复URL
      if (workspace.websites.some(w => w.url === url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: 'Website with this URL already exists in workspace',
          },
        };
      }

      // 获取网站标题（如果未提供）
      const websiteTitle = options.title || await this.getWebsiteTitle(url);

      // 创建网站对象
      const website: Website = {
        id: this.generateWebsiteId(),
        url,
        title: websiteTitle,
        favicon: options.favicon || await this.getFavicon(url),
        isPinned: options.pinTab || false,
        addedAt: Date.now(),
        order: workspace.websites.length,

        // 新增：记录原始信息用于精确匹配
        originalUrl: url,
        originalTitle: websiteTitle,

        // 设置默认匹配配置
        matchConfig: {
          strictTitleMatch: true,   // 严格匹配标题
          strictUrlMatch: true,     // 严格匹配URL
          ignoreUrlParams: false    // 不忽略URL参数
        }
      };

      // 添加到工作区
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      // 如果需要在新标签页中打开
      if (options.openInNewTab) {
        await chrome.tabs.create({
          url,
          pinned: options.pinTab || false
        });
      }

      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to add website',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId: string, websiteId: string): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      const websiteIndex = workspace.websites.findIndex(w => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`,
          },
        };
      }

      // 移除网站
      workspace.websites.splice(websiteIndex, 1);

      // 重新排序
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });

      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to remove website',
          details: error,
        },
      };
    }
  }

  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }): Promise<OperationResult<Website>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      const website = workspace.websites.find(w => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`,
          },
        };
      }

      // 更新网站信息
      if (updates.url !== undefined) {
        // 验证URL格式
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: 'Invalid URL format',
            },
          };
        }
        website.url = updates.url;
        // 如果URL改变了，更新favicon
        website.favicon = await this.getFavicon(updates.url);
      }

      if (updates.title !== undefined) {
        website.title = updates.title;
      }

      if (updates.isPinned !== undefined) {
        website.isPinned = updates.isPinned;
      }

      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }

      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to update website',
          details: error,
        },
      };
    }
  }

  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds: string[]): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;

      // 按新顺序重新排列
      const reorderedWorkspaces: WorkSpace[] = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find(w => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to reorder workspaces',
          details: error,
        },
      };
    }
  }

  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId: string, websiteIds: string[]): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`,
          },
        };
      }

      // 按新顺序重新排列网站
      const reorderedWebsites: Website[] = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find(w => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });

      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to reorder websites',
          details: error,
        },
      };
    }
  }

  /**
   * 设置工作区用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(
    workspaceId: string,
    isHidden: boolean,
    hiddenTabIds?: number[]
  ): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error,
        };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 更新隐藏状态
      workspace.userTabsHidden = isHidden;
      workspace.hiddenUserTabIds = hiddenTabIds || [];
      workspace.updatedAt = Date.now();

      // 用户标签页隐藏状态已更新

      // 保存更改
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set user tabs hidden state',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
  }>> {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return {
          success: false,
          error: workspaceResult.error,
        };
      }

      const workspace = workspaceResult.data!;

      return {
        success: true,
        data: {
          isHidden: workspace.userTabsHidden || false,
          hiddenTabIds: workspace.hiddenUserTabIds || [],
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get user tabs hidden state',
          details: error,
        },
      };
    }
  }

  /**
   * 清除工作区的隐藏标签页记录（当标签页被永久删除时调用）
   */
  static async clearHiddenTabIds(workspaceId: string, tabIdsToRemove: number[]): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error,
        };
      }

      const workspaces = workspacesResult.data!;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'Workspace not found',
          },
        };
      }

      // 从隐藏列表中移除指定的标签页ID
      if (workspace.hiddenUserTabIds) {
        workspace.hiddenUserTabIds = workspace.hiddenUserTabIds.filter(
          id => !tabIdsToRemove.includes(id)
        );
        workspace.updatedAt = Date.now();

        console.log(`从工作区 "${workspace.name}" 的隐藏列表中移除标签页ID: ${tabIdsToRemove.join(', ')}`);

        // 保存更改
        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) return saveResult;
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to clear hidden tab IDs',
          details: error,
        },
      };
    }
  }
}
