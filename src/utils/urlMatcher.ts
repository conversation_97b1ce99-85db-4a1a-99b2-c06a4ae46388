/**
 * 统一的URL域名匹配逻辑工具
 * 用于解决标签页检测不一致的问题
 */

/**
 * 标准化URL，移除常见的变体差异
 */
export function normalizeUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    
    // 移除www前缀
    let hostname = urlObj.hostname;
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    // 统一协议为https（用于比较）
    urlObj.protocol = 'https:';
    urlObj.hostname = hostname;
    
    // 移除尾部斜杠
    let pathname = urlObj.pathname;
    if (pathname.endsWith('/') && pathname.length > 1) {
      pathname = pathname.slice(0, -1);
    }
    urlObj.pathname = pathname;
    
    // 移除默认端口
    if ((urlObj.protocol === 'https:' && urlObj.port === '443') ||
        (urlObj.protocol === 'http:' && urlObj.port === '80')) {
      urlObj.port = '';
    }
    
    return urlObj.toString();
  } catch (error) {
    console.warn('URL标准化失败:', url, error);
    return url;
  }
}

/**
 * 提取域名（标准化后）
 */
export function extractDomain(url: string): string {
  try {
    const urlObj = new URL(url);
    let hostname = urlObj.hostname;
    
    // 移除www前缀
    if (hostname.startsWith('www.')) {
      hostname = hostname.substring(4);
    }
    
    return hostname;
  } catch (error) {
    console.warn('域名提取失败:', url, error);
    return '';
  }
}

/**
 * 检查两个URL是否指向同一个网站
 * 使用域名匹配逻辑
 */
export function isSameWebsite(url1: string, url2: string): boolean {
  try {
    const domain1 = extractDomain(url1);
    const domain2 = extractDomain(url2);
    
    if (!domain1 || !domain2) {
      return false;
    }
    
    return domain1 === domain2;
  } catch (error) {
    console.warn('网站匹配检查失败:', url1, url2, error);
    return false;
  }
}

/**
 * 检查标签页URL是否匹配网站URL
 * 支持前缀匹配和域名匹配两种模式
 */
export function isTabMatchingWebsite(tabUrl: string, websiteUrl: string, useStrictMatch = false): boolean {
  try {
    if (useStrictMatch) {
      // 严格匹配：标准化后的URL前缀匹配
      const normalizedTabUrl = normalizeUrl(tabUrl);
      const normalizedWebsiteUrl = normalizeUrl(websiteUrl);
      return normalizedTabUrl.startsWith(normalizedWebsiteUrl);
    } else {
      // 宽松匹配：域名匹配
      return isSameWebsite(tabUrl, websiteUrl);
    }
  } catch (error) {
    console.warn('标签页网站匹配失败:', tabUrl, websiteUrl, error);
    return false;
  }
}

/**
 * 为chrome.tabs.query()生成URL查询模式
 * 支持精确匹配和域名匹配
 */
export function generateTabQueryPatterns(websiteUrl: string): string[] {
  try {
    const urlObj = new URL(websiteUrl);
    const patterns: string[] = [];
    
    // 原始URL
    patterns.push(websiteUrl);
    
    // 移除尾部斜杠的版本
    if (websiteUrl.endsWith('/')) {
      patterns.push(websiteUrl.slice(0, -1));
    } else {
      patterns.push(websiteUrl + '/');
    }
    
    // www变体
    const hostname = urlObj.hostname;
    if (hostname.startsWith('www.')) {
      // 移除www的版本
      const noWwwUrl = websiteUrl.replace('www.', '');
      patterns.push(noWwwUrl);
      if (noWwwUrl.endsWith('/')) {
        patterns.push(noWwwUrl.slice(0, -1));
      } else {
        patterns.push(noWwwUrl + '/');
      }
    } else {
      // 添加www的版本
      const wwwUrl = websiteUrl.replace('://', '://www.');
      patterns.push(wwwUrl);
      if (wwwUrl.endsWith('/')) {
        patterns.push(wwwUrl.slice(0, -1));
      } else {
        patterns.push(wwwUrl + '/');
      }
    }
    
    // 协议变体
    if (urlObj.protocol === 'https:') {
      const httpUrl = websiteUrl.replace('https://', 'http://');
      patterns.push(httpUrl);
      if (httpUrl.endsWith('/')) {
        patterns.push(httpUrl.slice(0, -1));
      } else {
        patterns.push(httpUrl + '/');
      }
    } else if (urlObj.protocol === 'http:') {
      const httpsUrl = websiteUrl.replace('http://', 'https://');
      patterns.push(httpsUrl);
      if (httpsUrl.endsWith('/')) {
        patterns.push(httpsUrl.slice(0, -1));
      } else {
        patterns.push(httpsUrl + '/');
      }
    }
    
    // 去重并返回
    return [...new Set(patterns)];
  } catch (error) {
    console.warn('生成标签页查询模式失败:', websiteUrl, error);
    return [websiteUrl];
  }
}

/**
 * 调试函数：显示URL匹配信息
 */
export function debugUrlMatch(tabUrl: string, websiteUrl: string): void {
  console.log('🔍 URL匹配调试信息:');
  console.log('  标签页URL:', tabUrl);
  console.log('  网站URL:', websiteUrl);
  console.log('  标准化标签页URL:', normalizeUrl(tabUrl));
  console.log('  标准化网站URL:', normalizeUrl(websiteUrl));
  console.log('  标签页域名:', extractDomain(tabUrl));
  console.log('  网站域名:', extractDomain(websiteUrl));
  console.log('  域名匹配:', isSameWebsite(tabUrl, websiteUrl));
  console.log('  严格匹配:', isTabMatchingWebsite(tabUrl, websiteUrl, true));
  console.log('  宽松匹配:', isTabMatchingWebsite(tabUrl, websiteUrl, false));
}
