import { Settings, WorkspaceTemplate } from '@/types/workspace';

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  WORKSPACES: 'workspaces',
  SETTINGS: 'settings',
  ACTIVE_WORKSPACE_ID: 'activeWorkspaceId',
  LAST_ACTIVE_WORKSPACE_IDS: 'lastActiveWorkspaceIds',
} as const;

/**
 * 默认设置
 */
export const DEFAULT_SETTINGS: Settings = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: '',
  sidebarWidth: 320,
  theme: 'dark',
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5,
};

/**
 * 工作区颜色预设
 */
export const WORKSPACE_COLORS = [
  '#3b82f6', // blue
  '#10b981', // emerald
  '#f59e0b', // amber
  '#ef4444', // red
  '#8b5cf6', // violet
  '#06b6d4', // cyan
  '#84cc16', // lime
  '#f97316', // orange
  '#ec4899', // pink
  '#6366f1', // indigo
] as const;

/**
 * 工作区图标预设
 */
export const WORKSPACE_ICONS = [
  '🚀', '💼', '🔬', '🎨', '📊', '🛠️', '📚', '💡', '🎯', '⚡',
  '🌟', '🔥', '💎', '🎪', '🎭', '🎨', '🎵', '🎮', '🏆', '🎊',
  '📱', '💻', '🖥️', '⌨️', '🖱️', '🖨️', '📷', '📹', '🎥', '📺',
  '🔍', '🔎', '🔬', '🔭', '📡', '🛰️', '🚁', '✈️', '🛸',
] as const;

/**
 * 预设工作区模板
 */
export const WORKSPACE_TEMPLATES: WorkspaceTemplate[] = [
  {
    id: 'ai-tools',
    name: 'AI工具集',
    description: '常用的AI工具和平台',
    icon: '🤖',
    color: '#3b82f6',
    category: 'ai-tools',
    websites: [
      {
        url: 'https://chat.openai.com',
        title: 'ChatGPT',
        favicon: 'https://chat.openai.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://claude.ai',
        title: 'Claude',
        favicon: 'https://claude.ai/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://gemini.google.com',
        title: 'Gemini',
        favicon: 'https://gemini.google.com/favicon.ico',
        isPinned: true,
      },
    ],
  },
  {
    id: 'development',
    name: '开发环境',
    description: '编程开发相关工具',
    icon: '💻',
    color: '#10b981',
    category: 'development',
    websites: [
      {
        url: 'https://github.com',
        title: 'GitHub',
        favicon: 'https://github.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://stackoverflow.com',
        title: 'Stack Overflow',
        favicon: 'https://stackoverflow.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://developer.mozilla.org',
        title: 'MDN Web Docs',
        favicon: 'https://developer.mozilla.org/favicon.ico',
        isPinned: true,
      },
    ],
  },
  {
    id: 'design',
    name: '设计工具',
    description: '设计和创意工具',
    icon: '🎨',
    color: '#f59e0b',
    category: 'design',
    websites: [
      {
        url: 'https://www.figma.com',
        title: 'Figma',
        favicon: 'https://www.figma.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://dribbble.com',
        title: 'Dribbble',
        favicon: 'https://dribbble.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://www.behance.net',
        title: 'Behance',
        favicon: 'https://www.behance.net/favicon.ico',
        isPinned: true,
      },
    ],
  },
  {
    id: 'research',
    name: '学术研究',
    description: '学术研究和论文查找',
    icon: '🔬',
    color: '#8b5cf6',
    category: 'research',
    websites: [
      {
        url: 'https://arxiv.org',
        title: 'arXiv',
        favicon: 'https://arxiv.org/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://scholar.google.com',
        title: 'Google Scholar',
        favicon: 'https://scholar.google.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://www.researchgate.net',
        title: 'ResearchGate',
        favicon: 'https://www.researchgate.net/favicon.ico',
        isPinned: true,
      },
    ],
  },
  {
    id: 'productivity',
    name: '生产力工具',
    description: '提高工作效率的工具',
    icon: '⚡',
    color: '#06b6d4',
    category: 'productivity',
    websites: [
      {
        url: 'https://notion.so',
        title: 'Notion',
        favicon: 'https://notion.so/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://trello.com',
        title: 'Trello',
        favicon: 'https://trello.com/favicon.ico',
        isPinned: true,
      },
      {
        url: 'https://calendar.google.com',
        title: 'Google Calendar',
        favicon: 'https://calendar.google.com/favicon.ico',
        isPinned: true,
      },
    ],
  },
];

/**
 * 错误代码常量
 */
export const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: 'WORKSPACE_NOT_FOUND',
  WEBSITE_NOT_FOUND: 'WEBSITE_NOT_FOUND',
  STORAGE_ERROR: 'STORAGE_ERROR',
  TAB_ERROR: 'TAB_ERROR',
  WINDOW_ERROR: 'WINDOW_ERROR',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  INVALID_URL: 'INVALID_URL',
  DUPLICATE_WORKSPACE: 'DUPLICATE_WORKSPACE',
  DUPLICATE_WEBSITE: 'DUPLICATE_WEBSITE',
} as const;

/**
 * 事件名称常量
 */
export const EVENT_NAMES = {
  WORKSPACE_CHANGED: 'workspace-changed',
  SETTINGS_CHANGED: 'settings-changed',
  TAB_UPDATED: 'tab-updated',
} as const;

/**
 * 快捷键命令常量
 */
export const COMMANDS = {
  SWITCH_WORKSPACE_1: 'switch-workspace-1',
  SWITCH_WORKSPACE_2: 'switch-workspace-2',
  SWITCH_WORKSPACE_3: 'switch-workspace-3',
  TOGGLE_SIDEPANEL: 'toggle-sidepanel',
} as const;

/**
 * URL验证正则表达式
 */
export const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

/**
 * 默认网站图标
 */
export const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

/**
 * 侧边栏最小/最大宽度
 */
export const SIDEBAR_WIDTH = {
  MIN: 280,
  MAX: 500,
  DEFAULT: 320,
} as const;
