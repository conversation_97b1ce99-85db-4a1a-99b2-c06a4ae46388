import {
  TabInfo,
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 标签页管理类
 */
export class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,

      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab(): Promise<OperationResult<TabInfo>> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active tab found',
          },
        };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get active tab',
          details: error,
        },
      };
    }
  }

  /**
   * 检查URL是否已在当前窗口的标签页中打开（工作区隔离优化版）
   */
  static async findTabByUrl(url: string): Promise<OperationResult<TabInfo | null>> {
    try {
      // 首先尝试在当前窗口中精确匹配
      let tabs = await chrome.tabs.query({ url, currentWindow: true });

      // 如果精确匹配失败，尝试在当前窗口中进行域名匹配
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const currentWindowTabs = await chrome.tabs.query({ currentWindow: true });
          tabs = currentWindowTabs.filter(tab => {
            if (!tab.url) return false;
            try {
              const tabDomain = new URL(tab.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          // URL解析失败，返回空结果
          return { success: true, data: null };
        }
      }

      if (tabs.length === 0) {
        return { success: true, data: null };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to find tab by URL',
          details: error,
        },
      };
    }
  }

  /**
   * 创建新标签页
   */
  static async createTab(url: string, pinned: boolean = false, active: boolean = true): Promise<OperationResult<TabInfo>> {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active,
      });

      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab',
          details: error,
        },
      };
    }
  }

  /**
   * 激活标签页
   */
  static async activateTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to activate tab',
          details: error,
        },
      };
    }
  }

  /**
   * 固定/取消固定标签页
   */
  static async pinTab(tabId: number, pinned: boolean): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.update(tabId, { pinned });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to pin/unpin tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭标签页
   */
  static async closeTab(tabId: number): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds: number[]): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,

      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get current window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找工作区 "${workspace.name}" 相关的标签页`);

      // 只在当前窗口中查找相关标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      console.log(`工作区包含 ${workspaceUrls.length} 个网站:`, workspaceUrls);

      const relatedTabs = currentTabs.filter(tab => {
        const isRelated = workspaceUrls.some(url => tab.url.startsWith(url));
        if (isRelated) {
          console.log(`找到相关标签页: ${tab.title} (${tab.url})`);
        }
        return isRelated;
      });

      console.log(`工作区 "${workspace.name}" 共找到 ${relatedTabs.length} 个相关标签页`);
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error('获取工作区相关标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);

      // 只在当前窗口中查找非相关标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      const nonRelatedTabs = currentTabs.filter(tab =>
        !workspaceUrls.some(url => tab.url.startsWith(url))
      );

      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get non-workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId: number): Promise<boolean> {
    try {
      // 这里可以实现更复杂的逻辑来判断标签页是否为用户手动打开
      // 目前简单地检查标签页是否为固定状态
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }

  /**
   * 获取当前工作区中用户自行打开的标签页（非工作区配置的标签页）
   */
  static async getUserOpenedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找工作区 "${workspace.name}" 中用户自行打开的标签页`);

      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      console.log(`工作区配置的网站:`, workspaceUrls);

      // 过滤出不属于工作区配置的标签页
      const userOpenedTabs = currentTabs.filter(tab => {
        // 排除Chrome内部页面和扩展页面
        if (tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('edge://') ||
            tab.url.startsWith('about:')) {
          return false;
        }

        // 检查是否属于工作区配置的网站
        const isWorkspaceConfigured = workspaceUrls.some(url => tab.url.startsWith(url));

        // 用户自行打开的标签页

        return !isWorkspaceConfigured;
      });

      console.log(`工作区 "${workspace.name}" 中用户自行打开的标签页共 ${userOpenedTabs.length} 个`);
      return { success: true, data: userOpenedTabs };
    } catch (error) {
      console.error('获取用户自行打开的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user opened tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区中配置的标签页（工作区设置中明确添加的网站标签页）
   */
  static async getWorkspaceConfiguredTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      // 过滤出属于工作区配置的标签页
      const configuredTabs = currentTabs.filter(tab => {
        return workspaceUrls.some(url => tab.url.startsWith(url));
      });

      console.log(`工作区 "${workspace.name}" 中配置的标签页共 ${configuredTabs.length} 个`);
      return { success: true, data: configuredTabs };
    } catch (error) {
      console.error('获取工作区配置的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace configured tabs',
          details: error,
        },
      };
    }
  }
}

/**
 * 全局用户标签页隐藏/显示管理类
 */
export class GlobalUserTabsVisibilityManager {
  /**
   * 获取全局用户标签页隐藏状态
   */
  static async getGlobalUserTabsState(): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    totalUserTabs: number;
  }>> {
    try {
      // 从存储中获取全局隐藏状态
      const result = await chrome.storage.local.get(['globalUserTabsHidden', 'globalHiddenTabIds']);
      const isHidden = result.globalUserTabsHidden || false;
      const hiddenTabIds = result.globalHiddenTabIds || [];

      // 获取当前所有用户标签页数量
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      // 计算用户标签页（排除工作区网站和占位符页面）
      const allTabs = allTabsResult.data!;
      const userTabs = allTabs.filter(tab => {
        return !tab.url.includes('workspace-placeholder.html') &&
               !tab.url.includes('chrome-extension://') &&
               !tab.url.includes('chrome://');
      });

      return {
        success: true,
        data: {
          isHidden,
          hiddenTabIds,
          totalUserTabs: userTabs.length,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get global user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 设置全局用户标签页隐藏状态
   */
  static async setGlobalUserTabsState(isHidden: boolean, hiddenTabIds: number[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        globalUserTabsHidden: isHidden,
        globalHiddenTabIds: hiddenTabIds,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set global user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 全局隐藏所有用户标签页
   */
  static async hideAllUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔒 开始全局隐藏所有用户标签页');

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;

      // 筛选出用户标签页（排除工作区占位符和扩展页面）
      const userTabs = allTabs.filter(tab => {
        return !tab.url.includes('workspace-placeholder.html') &&
               !tab.url.includes('chrome-extension://') &&
               !tab.url.includes('chrome://') &&
               !tab.isPinned; // 也排除固定标签页
      });

      if (userTabs.length === 0) {
        console.log('⚠️ 没有用户标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页`);

      // 创建全局隐藏窗口
      const { WindowManager } = await import('./windowManager');
      const tabIds = userTabs.map(tab => tab.id);

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        'global-hidden-tabs',
        '隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到隐藏窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 保存全局隐藏状态
      const saveResult = await this.setGlobalUserTabsState(true, tabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error,
        };
      }

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error('❌ 全局隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide all user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 全局显示所有隐藏的用户标签页
   */
  static async showAllUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔓 开始全局显示所有隐藏的用户标签页');

      // 获取隐藏状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log('⚠️ 没有隐藏的用户标签页需要显示');
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log('⚠️ 所有隐藏的标签页都已不存在');
        await this.setGlobalUserTabsState(false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 移动隐藏的标签页回到主窗口
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1 // 移动到窗口末尾
      });

      // 清除全局隐藏状态
      const clearResult = await this.setGlobalUserTabsState(false, []);
      if (!clearResult.success) {
        return {
          success: false,
          error: clearResult.error,
        };
      }

      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error('❌ 全局显示用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show all user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 切换全局用户标签页的显示/隐藏状态
   */
  static async toggleGlobalUserTabsVisibility(): Promise<OperationResult<{
    action: 'hidden' | 'shown';
    tabIds: number[];
  }>> {
    try {
      console.log('🔄 切换全局用户标签页的显示状态');

      // 获取当前隐藏状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden } = stateResult.data!;

      if (isHidden) {
        // 当前是隐藏状态，执行显示操作
        const showResult = await this.showAllUserTabs();
        if (!showResult.success) {
          return {
            success: false,
            error: showResult.error,
          };
        }

        console.log(`✅ 全局用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'shown',
            tabIds: showResult.data!,
          },
        };
      } else {
        // 当前是显示状态，执行隐藏操作
        const hideResult = await this.hideAllUserTabs();
        if (!hideResult.success) {
          return {
            success: false,
            error: hideResult.error,
          };
        }

        console.log(`✅ 全局用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'hidden',
            tabIds: hideResult.data!,
          },
        };
      }
    } catch (error) {
      console.error('❌ 切换全局用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle global user tabs visibility',
          details: error,
        },
      };
    }
  }
}

/**
 * 用户标签页隐藏/显示管理类（保留向后兼容性）
 * @deprecated 请使用 GlobalUserTabsVisibilityManager
 */
export class UserTabsVisibilityManager {
  /**
   * 隐藏当前工作区中用户自行打开的标签页
   */
  static async hideUserTabs(workspace: WorkSpace): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔒 开始隐藏工作区 "${workspace.name}" 中的用户标签页`);

      // 获取用户自行打开的标签页
      const userTabsResult = await TabManager.getUserOpenedTabs(workspace);
      if (!userTabsResult.success) {
        return {
          success: false,
          error: userTabsResult.error,
        };
      }

      const userTabs = userTabsResult.data!;
      if (userTabs.length === 0) {
        console.log('⚠️ 没有用户自行打开的标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页`);

      // 移动用户标签页到专用窗口
      const tabIds = userTabs.map(tab => tab.id);
      const { WindowManager } = await import('./windowManager');

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        `${workspace.name} - 隐藏的用户标签页`
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到专用窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新工作区的隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const updateResult = await WorkspaceManager.setUserTabsHiddenState(
        workspace.id,
        true,
        tabIds
      );

      if (!updateResult.success) {
        console.error('❌ 更新工作区隐藏状态失败:', updateResult.error);
        return {
          success: false,
          error: updateResult.error,
        };
      }

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error('❌ 隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示当前工作区中已隐藏的用户标签页
   */
  static async showUserTabs(workspace: WorkSpace): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔓 开始显示工作区 "${workspace.name}" 中的隐藏用户标签页`);

      // 获取隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log('⚠️ 没有隐藏的用户标签页需要显示');
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log('⚠️ 所有隐藏的标签页都已不存在');
        // 清理隐藏状态
        await WorkspaceManager.setUserTabsHiddenState(workspace.id, false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 移动隐藏的标签页回到主窗口
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1 // 移动到窗口末尾
      });

      // 更新工作区的隐藏状态
      const updateResult = await WorkspaceManager.setUserTabsHiddenState(
        workspace.id,
        false,
        []
      );

      if (!updateResult.success) {
        console.error('❌ 更新工作区隐藏状态失败:', updateResult.error);
        return {
          success: false,
          error: updateResult.error,
        };
      }

      // 如果有标签页已不存在，清理记录
      const removedTabIds = hiddenTabIds.filter(id => !existingTabIds.includes(id));
      if (removedTabIds.length > 0) {
        await WorkspaceManager.clearHiddenTabIds(workspace.id, removedTabIds);
      }

      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error('❌ 显示用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 切换用户标签页的显示/隐藏状态
   */
  static async toggleUserTabsVisibility(workspace: WorkSpace): Promise<OperationResult<{
    action: 'hidden' | 'shown';
    tabIds: number[];
  }>> {
    try {
      console.log(`🔄 切换工作区 "${workspace.name}" 用户标签页的显示状态`);

      // 获取当前隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden } = stateResult.data!;

      if (isHidden) {
        // 当前是隐藏状态，执行显示操作
        const showResult = await this.showUserTabs(workspace);
        if (!showResult.success) {
          return {
            success: false,
            error: showResult.error,
          };
        }

        // 显示操作完成后，确保状态正确更新
        console.log(`✅ 用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'shown',
            tabIds: showResult.data!,
          },
        };
      } else {
        // 当前是显示状态，执行隐藏操作
        const hideResult = await this.hideUserTabs(workspace);
        if (!hideResult.success) {
          return {
            success: false,
            error: hideResult.error,
          };
        }

        // 隐藏操作完成后，确保状态正确更新
        console.log(`✅ 用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'hidden',
            tabIds: hideResult.data!,
          },
        };
      }
    } catch (error) {
      console.error('❌ 切换用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 获取用户标签页的当前显示状态
   */
  static async getUserTabsVisibilityState(workspace: WorkSpace): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabsCount: number;
    userTabsCount: number;
  }>> {
    try {
      // 获取隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      // 获取当前用户标签页数量
      const userTabsResult = await TabManager.getUserOpenedTabs(workspace);
      const userTabsCount = userTabsResult.success ? userTabsResult.data!.length : 0;

      return {
        success: true,
        data: {
          isHidden,
          hiddenTabsCount: hiddenTabIds.length,
          userTabsCount,
        },
      };
    } catch (error) {
      console.error('❌ 获取用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user tabs visibility state',
          details: error,
        },
      };
    }
  }
}
