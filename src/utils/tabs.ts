import {
  TabInfo,
  WorkSpace,
  OperationResult
} from '@/types/workspace';
import { ERROR_CODES } from './constants';

/**
 * 标签页管理类
 */
export class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,

      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab(): Promise<OperationResult<TabInfo>> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active tab found',
          },
        };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get active tab',
          details: error,
        },
      };
    }
  }

  /**
   * 检查URL是否已在当前窗口的标签页中打开（工作区隔离优化版）
   */
  static async findTabByUrl(url: string): Promise<OperationResult<TabInfo | null>> {
    try {
      // 首先尝试在当前窗口中精确匹配
      let tabs = await chrome.tabs.query({ url, currentWindow: true });

      // 如果精确匹配失败，尝试在当前窗口中进行域名匹配
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const currentWindowTabs = await chrome.tabs.query({ currentWindow: true });
          tabs = currentWindowTabs.filter(tab => {
            if (!tab.url) return false;
            try {
              const tabDomain = new URL(tab.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          // URL解析失败，返回空结果
          return { success: true, data: null };
        }
      }

      if (tabs.length === 0) {
        return { success: true, data: null };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to find tab by URL',
          details: error,
        },
      };
    }
  }

  /**
   * 创建新标签页
   */
  static async createTab(url: string, pinned: boolean = false, active: boolean = true): Promise<OperationResult<TabInfo>> {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active,
      });

      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab',
          details: error,
        },
      };
    }
  }

  /**
   * 激活标签页
   */
  static async activateTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to activate tab',
          details: error,
        },
      };
    }
  }

  /**
   * 固定/取消固定标签页
   */
  static async pinTab(tabId: number, pinned: boolean): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.update(tabId, { pinned });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to pin/unpin tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭标签页
   */
  static async closeTab(tabId: number): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds: number[]): Promise<OperationResult<void>> {
    try {
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,

      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get current window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区真正管理的标签页（仅在当前窗口中查找，排除用户自行打开的同URL标签页）
   */
  static async getWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找工作区 "${workspace.name}" 真正管理的标签页`);

      // 只在当前窗口中查找相关标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceWebsites = workspace.websites;

      console.log(`工作区包含 ${workspaceWebsites.length} 个网站:`, workspaceWebsites.map(w => `${w.url} (固定:${w.isPinned})`));

      const relatedTabs: TabInfo[] = [];

      for (const tab of currentTabs) {
        // 检查是否匹配工作区的网站配置
        const matchingWebsite = workspaceWebsites.find(website => {
          if (!tab.url.startsWith(website.url)) return false;

          // URL匹配，进一步检查属性是否匹配
          // 如果固定状态匹配，认为是工作区管理的标签页
          return tab.isPinned === website.isPinned;
        });

        if (matchingWebsite) {
          console.log(`✅ 找到工作区管理的标签页: ${tab.title} (${tab.url}) - 固定状态匹配: ${tab.isPinned}`);
          relatedTabs.push(tab);
        } else {
          // 检查是否有URL匹配但属性不匹配的情况
          const urlMatching = workspaceWebsites.find(website => tab.url.startsWith(website.url));
          if (urlMatching) {
            console.log(`⚠️ 发现用户自行打开的同URL标签页: ${tab.title} (${tab.url}) - 工作区配置固定:${urlMatching.isPinned}, 标签页固定:${tab.isPinned}`);
          }
        }
      }

      console.log(`工作区 "${workspace.name}" 真正管理的标签页共 ${relatedTabs.length} 个`);
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error('获取工作区相关标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);

      // 只在当前窗口中查找非相关标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      const nonRelatedTabs = currentTabs.filter(tab =>
        !workspaceUrls.some(url => tab.url.startsWith(url))
      );

      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get non-workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId: number): Promise<boolean> {
    try {
      // 这里可以实现更复杂的逻辑来判断标签页是否为用户手动打开
      // 目前简单地检查标签页是否为固定状态
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }

  /**
   * 获取当前工作区中用户自行打开的标签页（非工作区配置的标签页）
   */
  static async getUserOpenedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找工作区 "${workspace.name}" 中用户自行打开的标签页`);

      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      console.log(`工作区配置的网站:`, workspaceUrls);

      // 过滤出不属于工作区配置的标签页
      const userOpenedTabs = currentTabs.filter(tab => {
        // 排除Chrome内部页面和扩展页面
        if (tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('edge://') ||
            tab.url.startsWith('about:')) {
          return false;
        }

        // 检查是否属于工作区配置的网站
        const isWorkspaceConfigured = workspaceUrls.some(url => tab.url.startsWith(url));

        // 用户自行打开的标签页

        return !isWorkspaceConfigured;
      });

      console.log(`工作区 "${workspace.name}" 中用户自行打开的标签页共 ${userOpenedTabs.length} 个`);
      return { success: true, data: userOpenedTabs };
    } catch (error) {
      console.error('获取用户自行打开的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user opened tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区中配置的标签页（工作区设置中明确添加的网站标签页）
   */
  static async getWorkspaceConfiguredTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      // 过滤出属于工作区配置的标签页
      const configuredTabs = currentTabs.filter(tab => {
        return workspaceUrls.some(url => tab.url.startsWith(url));
      });

      console.log(`工作区 "${workspace.name}" 中配置的标签页共 ${configuredTabs.length} 个`);
      return { success: true, data: configuredTabs };
    } catch (error) {
      console.error('获取工作区配置的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace configured tabs',
          details: error,
        },
      };
    }
  }
}

/**
 * 标签页继承追踪系统
 */
export class TabInheritanceTracker {
  private static readonly TAB_INHERITANCE_KEY = 'tab_inheritance_tracking';

  /**
   * 记录标签页的工作区继承关系
   */
  static async recordTabInheritance(
    tabId: number,
    workspaceId: string,
    websiteId: string,
    parentTabId?: number
  ): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.TAB_INHERITANCE_KEY]);
      const inheritanceData = result[this.TAB_INHERITANCE_KEY] || {};

      inheritanceData[tabId] = {
        workspaceId,
        websiteId,
        parentTabId,
        createdAt: Date.now(),
        lastUpdated: Date.now()
      };

      await chrome.storage.local.set({
        [this.TAB_INHERITANCE_KEY]: inheritanceData
      });

      console.log(`📝 记录标签页继承关系: ${tabId} -> 工作区: ${workspaceId}, 网站: ${websiteId}`);
    } catch (error) {
      console.error('记录标签页继承关系失败:', error);
    }
  }

  /**
   * 检查标签页是否有工作区继承关系
   */
  static async getTabInheritance(tabId: number): Promise<{
    workspaceId: string;
    websiteId: string;
    parentTabId?: number;
  } | null> {
    try {
      const result = await chrome.storage.local.get([this.TAB_INHERITANCE_KEY]);
      const inheritanceData = result[this.TAB_INHERITANCE_KEY] || {};
      return inheritanceData[tabId] || null;
    } catch (error) {
      console.error('获取标签页继承关系失败:', error);
      return null;
    }
  }

  /**
   * 移除标签页继承记录
   */
  static async removeTabInheritance(tabId: number): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.TAB_INHERITANCE_KEY]);
      const inheritanceData = result[this.TAB_INHERITANCE_KEY] || {};

      if (inheritanceData[tabId]) {
        delete inheritanceData[tabId];
        await chrome.storage.local.set({
          [this.TAB_INHERITANCE_KEY]: inheritanceData
        });
        console.log(`🗑️ 移除标签页继承记录: ${tabId}`);
      }
    } catch (error) {
      console.error('移除标签页继承记录失败:', error);
    }
  }

  /**
   * 清理无效的继承记录
   */
  static async cleanupInvalidInheritance(): Promise<void> {
    try {
      const result = await chrome.storage.local.get([this.TAB_INHERITANCE_KEY]);
      const inheritanceData = result[this.TAB_INHERITANCE_KEY] || {};

      const tabIds = Object.keys(inheritanceData).map(id => parseInt(id));
      if (tabIds.length === 0) return;

      // 检查哪些标签页仍然存在
      const existingTabs = await chrome.tabs.query({});
      const existingTabIds = new Set(existingTabs.map(tab => tab.id));

      let hasChanges = false;
      for (const tabId of tabIds) {
        if (!existingTabIds.has(tabId)) {
          delete inheritanceData[tabId];
          hasChanges = true;
          console.log(`🧹 清理无效的标签页继承记录: ${tabId}`);
        }
      }

      if (hasChanges) {
        await chrome.storage.local.set({
          [this.TAB_INHERITANCE_KEY]: inheritanceData
        });
      }
    } catch (error) {
      console.error('清理无效继承记录失败:', error);
    }
  }
}

/**
 * 工作区标签页内容匹配系统
 */
export class WorkspaceTabContentMatcher {
  /**
   * 标准化URL，移除参数和片段（可选）
   */
  private static normalizeUrl(url: string, ignoreParams: boolean = false): string {
    try {
      const urlObj = new URL(url);

      if (ignoreParams) {
        // 移除查询参数和片段
        return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
      }

      // 保留查询参数，但移除片段
      return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}${urlObj.search}`;
    } catch (error) {
      // 如果URL解析失败，返回原始URL
      return url;
    }
  }

  /**
   * 获取URL的域名部分
   */
  private static getDomain(url: string, includeSubdomains: boolean = true): string {
    try {
      const urlObj = new URL(url);
      if (includeSubdomains) {
        return urlObj.hostname;
      } else {
        // 提取主域名（去除子域名）
        const parts = urlObj.hostname.split('.');
        if (parts.length >= 2) {
          return parts.slice(-2).join('.');
        }
        return urlObj.hostname;
      }
    } catch (error) {
      return '';
    }
  }

  /**
   * 智能URL匹配
   */
  private static matchUrl(
    tabUrl: string,
    websiteUrl: string,
    strategy: 'exact' | 'domain' | 'prefix' | 'flexible',
    ignoreParams: boolean = false,
    includeSubdomains: boolean = true
  ): boolean {
    try {
      switch (strategy) {
        case 'exact':
          // 完全匹配
          const normalizedTabUrl = this.normalizeUrl(tabUrl, ignoreParams);
          const normalizedWebsiteUrl = this.normalizeUrl(websiteUrl, ignoreParams);
          return normalizedTabUrl === normalizedWebsiteUrl;

        case 'domain':
          // 域名匹配
          const tabDomain = this.getDomain(tabUrl, includeSubdomains);
          const websiteDomain = this.getDomain(websiteUrl, includeSubdomains);
          return tabDomain === websiteDomain;

        case 'prefix':
          // 前缀匹配（网站内导航）
          const baseUrl = this.normalizeUrl(websiteUrl, true); // 移除参数
          const currentUrl = this.normalizeUrl(tabUrl, true);
          return currentUrl.startsWith(baseUrl);

        case 'flexible':
          // 灵活匹配：域名匹配 OR 前缀匹配
          return this.matchUrl(tabUrl, websiteUrl, 'domain', ignoreParams, includeSubdomains) ||
                 this.matchUrl(tabUrl, websiteUrl, 'prefix', ignoreParams, includeSubdomains);

        default:
          return false;
      }
    } catch (error) {
      console.error('URL匹配失败:', error);
      return false;
    }
  }

  /**
   * 智能标题匹配
   */
  private static matchTitle(
    tabTitle: string,
    websiteTitle: string,
    strategy: 'exact' | 'contains' | 'prefix' | 'flexible'
  ): boolean {
    const normalizedTabTitle = this.normalizeTitle(tabTitle);
    const normalizedWebsiteTitle = this.normalizeTitle(websiteTitle);

    if (!normalizedTabTitle || !normalizedWebsiteTitle) {
      return false;
    }

    switch (strategy) {
      case 'exact':
        // 完全匹配
        return normalizedTabTitle === normalizedWebsiteTitle;

      case 'contains':
        // 包含匹配
        return normalizedTabTitle.includes(normalizedWebsiteTitle) ||
               normalizedWebsiteTitle.includes(normalizedTabTitle);

      case 'prefix':
        // 前缀匹配
        return normalizedTabTitle.startsWith(normalizedWebsiteTitle) ||
               normalizedWebsiteTitle.startsWith(normalizedTabTitle);

      case 'flexible':
        // 灵活匹配：包含 OR 前缀
        return this.matchTitle(tabTitle, websiteTitle, 'contains') ||
               this.matchTitle(tabTitle, websiteTitle, 'prefix');

      default:
        return false;
    }
  }

  /**
   * 标准化标题，移除多余空白和特殊字符
   */
  private static normalizeTitle(title: string): string {
    return title
      .trim()
      .replace(/\s+/g, ' ') // 将多个空白字符替换为单个空格
      .replace(/[\u200B-\u200D\uFEFF]/g, ''); // 移除零宽字符
  }

  /**
   * 检查标签页是否与工作区网站匹配
   */
  static async isWorkspaceTab(tab: TabInfo): Promise<{
    isMatch: boolean;
    workspaceId?: string;
    websiteId?: string;
    matchType?: 'exact' | 'inheritance' | 'domain' | 'prefix' | 'flexible';
    matchReason?: string;
  }> {
    try {
      // 排除系统页面和扩展页面
      if (tab.url.includes('workspace-placeholder.html') ||
          tab.url.includes('chrome-extension://') ||
          tab.url.includes('chrome://') ||
          tab.url.includes('edge://') ||
          tab.url.includes('about:')) {
        return { isMatch: false };
      }

      // 首先检查标签页继承关系
      const inheritance = await TabInheritanceTracker.getTabInheritance(tab.id);
      if (inheritance) {
        console.log(`🧬 通过继承关系识别为工作区标签页: ${tab.url} -> 工作区: ${inheritance.workspaceId}`);
        return {
          isMatch: true,
          workspaceId: inheritance.workspaceId,
          websiteId: inheritance.websiteId,
          matchType: 'inheritance',
          matchReason: '标签页继承关系'
        };
      }

      // 获取所有工作区网站信息
      const workspaceWebsites = await this.getAllWorkspaceWebsitesWithDetails();

      // 遍历所有工作区网站进行智能匹配
      for (const website of workspaceWebsites) {
        const matchResult = this.intelligentMatchTabWithWebsite(tab, website);
        if (matchResult.isMatch) {
          // 如果匹配成功且启用了标签页继承，记录继承关系
          if (website.matchConfig?.enableTabInheritance) {
            await TabInheritanceTracker.recordTabInheritance(
              tab.id,
              website.workspaceId,
              website.websiteId
            );
          }

          return {
            isMatch: true,
            workspaceId: website.workspaceId,
            websiteId: website.websiteId,
            matchType: matchResult.matchType,
            matchReason: matchResult.matchReason
          };
        }
      }

      return { isMatch: false };
    } catch (error) {
      console.error('检查工作区标签页匹配失败:', error);
      return { isMatch: false };
    }
  }

  /**
   * 智能匹配标签页与特定网站
   */
  private static intelligentMatchTabWithWebsite(
    tab: TabInfo,
    website: any
  ): {
    isMatch: boolean;
    matchType?: 'exact' | 'domain' | 'prefix' | 'flexible';
    matchReason?: string;
  } {
    // 获取匹配配置，设置智能默认值
    const config = website.matchConfig || {
      urlMatchStrategy: 'flexible' as const,      // 默认使用灵活匹配
      titleMatchStrategy: 'flexible' as const,   // 默认使用灵活匹配
      ignoreUrlParams: false,
      enableTabInheritance: true,                 // 默认启用标签页继承
      includeSubdomains: true
    };

    // URL匹配检查
    const websiteUrl = website.originalUrl || website.url;
    const urlMatches = this.matchUrl(
      tab.url,
      websiteUrl,
      config.urlMatchStrategy,
      config.ignoreUrlParams,
      config.includeSubdomains
    );

    // 标题匹配检查
    const websiteTitle = website.originalTitle || website.title;
    const titleMatches = this.matchTitle(
      tab.title,
      websiteTitle,
      config.titleMatchStrategy
    );

    // 自定义规则检查
    if (config.customRules) {
      const customMatch = this.checkCustomRules(tab, config.customRules);
      if (customMatch.isMatch) {
        return {
          isMatch: true,
          matchType: 'flexible',
          matchReason: `自定义规则匹配: ${customMatch.reason}`
        };
      }
      if (customMatch.isExcluded) {
        return { isMatch: false };
      }
    }

    // 根据匹配策略决定结果
    if (config.urlMatchStrategy === 'exact' && config.titleMatchStrategy === 'exact') {
      // 严格模式：URL和标题都必须完全匹配
      if (urlMatches && titleMatches) {
        return {
          isMatch: true,
          matchType: 'exact',
          matchReason: 'URL和标题完全匹配'
        };
      }
    } else if (config.urlMatchStrategy === 'domain') {
      // 域名匹配模式：适用于网站内导航
      if (urlMatches) {
        return {
          isMatch: true,
          matchType: 'domain',
          matchReason: '域名匹配（网站内导航）'
        };
      }
    } else if (config.urlMatchStrategy === 'prefix') {
      // 前缀匹配模式：适用于子页面导航
      if (urlMatches) {
        return {
          isMatch: true,
          matchType: 'prefix',
          matchReason: 'URL前缀匹配（子页面导航）'
        };
      }
    } else {
      // 灵活匹配模式：综合考虑URL和标题
      if (urlMatches && titleMatches) {
        return {
          isMatch: true,
          matchType: 'flexible',
          matchReason: 'URL和标题灵活匹配'
        };
      } else if (urlMatches) {
        return {
          isMatch: true,
          matchType: 'flexible',
          matchReason: 'URL匹配（同网站导航）'
        };
      }
    }

    return { isMatch: false };
  }

  /**
   * 检查自定义匹配规则
   */
  private static checkCustomRules(
    tab: TabInfo,
    customRules: any
  ): { isMatch: boolean; isExcluded: boolean; reason?: string } {
    try {
      // 检查排除规则
      if (customRules.excludedUrlPatterns) {
        for (const pattern of customRules.excludedUrlPatterns) {
          const regex = new RegExp(pattern);
          if (regex.test(tab.url)) {
            return { isMatch: false, isExcluded: true };
          }
        }
      }

      // 检查允许的URL模式
      if (customRules.allowedUrlPatterns) {
        for (const pattern of customRules.allowedUrlPatterns) {
          const regex = new RegExp(pattern);
          if (regex.test(tab.url)) {
            return { isMatch: true, isExcluded: false, reason: `URL模式: ${pattern}` };
          }
        }
      }

      // 检查允许的标题模式
      if (customRules.allowedTitlePatterns) {
        for (const pattern of customRules.allowedTitlePatterns) {
          const regex = new RegExp(pattern);
          if (regex.test(tab.title)) {
            return { isMatch: true, isExcluded: false, reason: `标题模式: ${pattern}` };
          }
        }
      }

      return { isMatch: false, isExcluded: false };
    } catch (error) {
      console.error('检查自定义规则失败:', error);
      return { isMatch: false, isExcluded: false };
    }
  }

  /**
   * 获取所有工作区网站的详细信息
   */
  private static async getAllWorkspaceWebsitesWithDetails(): Promise<Array<{
    url: string;
    title: string;
    originalUrl?: string;
    originalTitle?: string;
    isPinned: boolean;
    workspaceId: string;
    websiteId: string;
    matchConfig?: any;
  }>> {
    try {
      const { StorageManager } = await import('./storage');
      const workspacesResult = await StorageManager.getWorkspaces();

      if (!workspacesResult.success || !workspacesResult.data) {
        return [];
      }

      const websites: any[] = [];

      for (const workspace of workspacesResult.data) {
        for (const website of workspace.websites) {
          websites.push({
            url: website.url,
            title: website.title,
            originalUrl: website.originalUrl,
            originalTitle: website.originalTitle,
            isPinned: website.isPinned,
            workspaceId: workspace.id,
            websiteId: website.id,
            matchConfig: website.matchConfig || {
              strictTitleMatch: true,
              strictUrlMatch: true,
              ignoreUrlParams: false
            }
          });
        }
      }

      return websites;
    } catch (error) {
      console.error('获取工作区网站详细信息失败:', error);
      return [];
    }
  }

  /**
   * 更新网站的原始信息（当标签页标题或URL发生变化时）
   */
  static async updateWebsiteOriginalInfo(
    workspaceId: string,
    websiteId: string,
    originalUrl: string,
    originalTitle: string
  ): Promise<void> {
    try {
      const { StorageManager } = await import('./storage');
      const workspacesResult = await StorageManager.getWorkspaces();

      if (!workspacesResult.success || !workspacesResult.data) {
        return;
      }

      const workspaces = workspacesResult.data;
      const workspace = workspaces.find(w => w.id === workspaceId);

      if (!workspace) {
        return;
      }

      const website = workspace.websites.find(w => w.id === websiteId);
      if (!website) {
        return;
      }

      // 更新原始信息
      website.originalUrl = originalUrl;
      website.originalTitle = originalTitle;

      // 如果没有匹配配置，设置智能默认配置
      if (!website.matchConfig) {
        website.matchConfig = {
          urlMatchStrategy: 'flexible' as const,
          titleMatchStrategy: 'flexible' as const,
          ignoreUrlParams: false,
          enableTabInheritance: true,
          includeSubdomains: true
        };
      }

      workspace.updatedAt = Date.now();

      // 保存更新
      await StorageManager.saveWorkspaces(workspaces);

      console.log(`✅ 已更新网站原始信息: ${websiteId} -> ${originalTitle} (${originalUrl})`);
    } catch (error) {
      console.error('更新网站原始信息失败:', error);
    }
  }
}

/**
 * 全局用户标签页隐藏/显示管理类
 */
export class GlobalUserTabsVisibilityManager {


  /**
   * 检查标签页是否为真正的用户标签页（非工作区管理的标签页）
   * 使用新的基于内容匹配的精确识别系统
   */
  private static async isRealUserTab(tab: TabInfo): Promise<boolean> {
    try {
      // 使用新的内容匹配系统
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(tab);

      if (matchResult.isMatch) {
        console.log(`🏢 通过内容匹配识别为工作区管理的标签页: ${tab.url} (${tab.title}) - 工作区: ${matchResult.workspaceId}, 匹配类型: ${matchResult.matchType}`);
        return false; // 这是工作区管理的标签页
      } else {
        console.log(`👤 识别为用户标签页: ${tab.url} (${tab.title})`);
        return true; // 这是用户标签页
      }
    } catch (error) {
      console.error('检查用户标签页失败:', error);
      // 出错时采用保守策略，认为是用户标签页
      return true;
    }
  }


  /**
   * 获取全局用户标签页隐藏状态
   */
  static async getGlobalUserTabsState(): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    totalUserTabs: number;
    visibleUserTabs: number;
    canContinueHiding: boolean; // 是否可以继续隐藏
    actionType: 'hide' | 'continue_hide' | 'show'; // 建议的操作类型
  }>> {
    try {
      // 从存储中获取全局隐藏状态
      const result = await chrome.storage.local.get(['globalUserTabsHidden', 'globalHiddenTabIds']);
      const hiddenTabIds = result.globalHiddenTabIds || [];

      // 验证隐藏的标签页是否仍然存在
      const validHiddenTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validHiddenTabIds.push(tabId);
        } catch {
          // 标签页已不存在，忽略
        }
      }

      // 如果有无效的隐藏标签页，更新存储
      if (validHiddenTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          globalHiddenTabIds: validHiddenTabIds
        });

        // 如果没有有效的隐藏标签页，清除隐藏状态
        if (validHiddenTabIds.length === 0) {
          await chrome.storage.local.set({
            globalUserTabsHidden: false
          });
        }
      }

      // 获取当前所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      // 计算真正的用户标签页（排除工作区管理的标签页）
      const allTabs = allTabsResult.data!;
      const allUserTabs = [];

      for (const tab of allTabs) {
        if (await this.isRealUserTab(tab)) {
          allUserTabs.push(tab);
        }
      }

      // 计算可见的用户标签页（在主窗口中的）
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = allUserTabs.filter(tab =>
        tab.windowId === currentWindow.id &&
        !validHiddenTabIds.includes(tab.id)
      );

      // 总用户标签页数量 = 可见的 + 隐藏的
      const totalUserTabs = visibleUserTabs.length + validHiddenTabIds.length;

      // 判断操作类型
      const hasHiddenTabs = validHiddenTabIds.length > 0;
      const hasVisibleTabs = visibleUserTabs.length > 0;

      let actionType: 'hide' | 'continue_hide' | 'show';
      let canContinueHiding = false;

      if (hasHiddenTabs && hasVisibleTabs) {
        // 既有隐藏的又有可见的 -> 继续隐藏
        actionType = 'continue_hide';
        canContinueHiding = true;
      } else if (hasHiddenTabs && !hasVisibleTabs) {
        // 只有隐藏的，没有可见的 -> 显示
        actionType = 'show';
        canContinueHiding = false;
      } else {
        // 没有隐藏的，只有可见的 -> 隐藏
        actionType = 'hide';
        canContinueHiding = false;
      }

      return {
        success: true,
        data: {
          isHidden: hasHiddenTabs,
          hiddenTabIds: validHiddenTabIds,
          totalUserTabs,
          visibleUserTabs: visibleUserTabs.length,
          canContinueHiding,
          actionType,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get global user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 设置全局用户标签页隐藏状态
   */
  static async setGlobalUserTabsState(isHidden: boolean, hiddenTabIds: number[]): Promise<OperationResult<void>> {
    try {
      await chrome.storage.local.set({
        globalUserTabsHidden: isHidden,
        globalHiddenTabIds: hiddenTabIds,
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set global user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 继续隐藏当前可见的用户标签页（保留已隐藏的标签页）
   */
  static async continueHideUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔒 继续隐藏当前可见的用户标签页');

      // 获取当前状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const currentState = stateResult.data!;

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中的可见用户标签页
      const visibleUserTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId === currentWindow.id &&
            await this.isRealUserTab(tab) &&
            !currentState.hiddenTabIds.includes(tab.id)) {
          visibleUserTabs.push(tab);
        }
      }

      if (visibleUserTabs.length === 0) {
        console.log('⚠️ 没有新的用户标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备继续隐藏 ${visibleUserTabs.length} 个可见的用户标签页`);

      // 移动新的可见标签页到隐藏窗口
      const { WindowManager } = await import('./windowManager');
      const newTabIds = visibleUserTabs.map(tab => tab.id);

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        newTabIds,
        'global-hidden-tabs',
        '隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到隐藏窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新隐藏状态，合并新的标签页ID
      const allHiddenTabIds = [...currentState.hiddenTabIds, ...newTabIds];
      const saveResult = await this.setGlobalUserTabsState(true, allHiddenTabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error,
        };
      }

      console.log(`✅ 成功继续隐藏 ${newTabIds.length} 个用户标签页`);
      return { success: true, data: newTabIds };
    } catch (error) {
      console.error('❌ 继续隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to continue hiding user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 全局隐藏所有用户标签页
   */
  static async hideAllUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔒 开始全局隐藏所有用户标签页');

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中的真正用户标签页（排除工作区管理的标签页）
      const userTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId === currentWindow.id && await this.isRealUserTab(tab)) {
          userTabs.push(tab);
        }
      }

      if (userTabs.length === 0) {
        console.log('⚠️ 没有用户标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页`);

      // 创建全局隐藏窗口
      const { WindowManager } = await import('./windowManager');
      const tabIds = userTabs.map(tab => tab.id);

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        'global-hidden-tabs',
        '隐藏的用户标签页'
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到隐藏窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 保存全局隐藏状态
      const saveResult = await this.setGlobalUserTabsState(true, tabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error,
        };
      }

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error('❌ 全局隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide all user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 全局显示所有隐藏的用户标签页
   */
  static async showAllUserTabs(): Promise<OperationResult<number[]>> {
    try {
      console.log('🔓 开始全局显示所有隐藏的用户标签页');

      // 获取隐藏状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log('⚠️ 没有隐藏的用户标签页需要显示');
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log('⚠️ 所有隐藏的标签页都已不存在');
        await this.setGlobalUserTabsState(false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 移动隐藏的标签页回到主窗口
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1 // 移动到窗口末尾
      });

      // 清除全局隐藏状态
      const clearResult = await this.setGlobalUserTabsState(false, []);
      if (!clearResult.success) {
        return {
          success: false,
          error: clearResult.error,
        };
      }

      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error('❌ 全局显示用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show all user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 切换全局用户标签页的显示/隐藏状态（智能模式）
   */
  static async toggleGlobalUserTabsVisibility(): Promise<OperationResult<{
    action: 'hidden' | 'shown' | 'continue_hidden';
    tabIds: number[];
  }>> {
    try {
      console.log('🔄 智能切换全局用户标签页的显示状态');

      // 获取当前状态
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { actionType } = stateResult.data!;

      switch (actionType) {
        case 'show':
          // 显示所有隐藏的标签页
          const showResult = await this.showAllUserTabs();
          if (!showResult.success) {
            return {
              success: false,
              error: showResult.error,
            };
          }

          console.log(`✅ 全局用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'shown',
              tabIds: showResult.data!,
            },
          };

        case 'continue_hide':
          // 继续隐藏当前可见的标签页
          const continueHideResult = await this.continueHideUserTabs();
          if (!continueHideResult.success) {
            return {
              success: false,
              error: continueHideResult.error,
            };
          }

          console.log(`✅ 继续隐藏用户标签页成功，影响 ${continueHideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'continue_hidden',
              tabIds: continueHideResult.data!,
            },
          };

        case 'hide':
        default:
          // 隐藏所有用户标签页
          const hideResult = await this.hideAllUserTabs();
          if (!hideResult.success) {
            return {
              success: false,
              error: hideResult.error,
            };
          }

          console.log(`✅ 全局用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'hidden',
              tabIds: hideResult.data!,
            },
          };
      }
    } catch (error) {
      console.error('❌ 切换全局用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle global user tabs visibility',
          details: error,
        },
      };
    }
  }
}

/**
 * 用户标签页隐藏/显示管理类（保留向后兼容性）
 * @deprecated 请使用 GlobalUserTabsVisibilityManager
 */
export class UserTabsVisibilityManager {
  /**
   * 隐藏当前工作区中用户自行打开的标签页
   */
  static async hideUserTabs(workspace: WorkSpace): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔒 开始隐藏工作区 "${workspace.name}" 中的用户标签页`);

      // 获取用户自行打开的标签页
      const userTabsResult = await TabManager.getUserOpenedTabs(workspace);
      if (!userTabsResult.success) {
        return {
          success: false,
          error: userTabsResult.error,
        };
      }

      const userTabs = userTabsResult.data!;
      if (userTabs.length === 0) {
        console.log('⚠️ 没有用户自行打开的标签页需要隐藏');
        return { success: true, data: [] };
      }

      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页`);

      // 移动用户标签页到专用窗口
      const tabIds = userTabs.map(tab => tab.id);
      const { WindowManager } = await import('./windowManager');

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        `${workspace.name} - 隐藏的用户标签页`
      );

      if (!moveResult.success) {
        console.error('❌ 移动用户标签页到专用窗口失败:', moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新工作区的隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const updateResult = await WorkspaceManager.setUserTabsHiddenState(
        workspace.id,
        true,
        tabIds
      );

      if (!updateResult.success) {
        console.error('❌ 更新工作区隐藏状态失败:', updateResult.error);
        return {
          success: false,
          error: updateResult.error,
        };
      }

      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error('❌ 隐藏用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示当前工作区中已隐藏的用户标签页
   */
  static async showUserTabs(workspace: WorkSpace): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔓 开始显示工作区 "${workspace.name}" 中的隐藏用户标签页`);

      // 获取隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log('⚠️ 没有隐藏的用户标签页需要显示');
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log('⚠️ 所有隐藏的标签页都已不存在');
        // 清理隐藏状态
        await WorkspaceManager.setUserTabsHiddenState(workspace.id, false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 移动隐藏的标签页回到主窗口
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id!,
        index: -1 // 移动到窗口末尾
      });

      // 更新工作区的隐藏状态
      const updateResult = await WorkspaceManager.setUserTabsHiddenState(
        workspace.id,
        false,
        []
      );

      if (!updateResult.success) {
        console.error('❌ 更新工作区隐藏状态失败:', updateResult.error);
        return {
          success: false,
          error: updateResult.error,
        };
      }

      // 如果有标签页已不存在，清理记录
      const removedTabIds = hiddenTabIds.filter(id => !existingTabIds.includes(id));
      if (removedTabIds.length > 0) {
        await WorkspaceManager.clearHiddenTabIds(workspace.id, removedTabIds);
      }

      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error('❌ 显示用户标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 切换用户标签页的显示/隐藏状态
   */
  static async toggleUserTabsVisibility(workspace: WorkSpace): Promise<OperationResult<{
    action: 'hidden' | 'shown';
    tabIds: number[];
  }>> {
    try {
      console.log(`🔄 切换工作区 "${workspace.name}" 用户标签页的显示状态`);

      // 获取当前隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden } = stateResult.data!;

      if (isHidden) {
        // 当前是隐藏状态，执行显示操作
        const showResult = await this.showUserTabs(workspace);
        if (!showResult.success) {
          return {
            success: false,
            error: showResult.error,
          };
        }

        // 显示操作完成后，确保状态正确更新
        console.log(`✅ 用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'shown',
            tabIds: showResult.data!,
          },
        };
      } else {
        // 当前是显示状态，执行隐藏操作
        const hideResult = await this.hideUserTabs(workspace);
        if (!hideResult.success) {
          return {
            success: false,
            error: hideResult.error,
          };
        }

        // 隐藏操作完成后，确保状态正确更新
        console.log(`✅ 用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);

        return {
          success: true,
          data: {
            action: 'hidden',
            tabIds: hideResult.data!,
          },
        };
      }
    } catch (error) {
      console.error('❌ 切换用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 获取用户标签页的当前显示状态
   */
  static async getUserTabsVisibilityState(workspace: WorkSpace): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabsCount: number;
    userTabsCount: number;
  }>> {
    try {
      // 获取隐藏状态
      const { WorkspaceManager } = await import('./workspace');
      const stateResult = await WorkspaceManager.getUserTabsHiddenState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds } = stateResult.data!;

      // 获取当前用户标签页数量
      const userTabsResult = await TabManager.getUserOpenedTabs(workspace);
      const userTabsCount = userTabsResult.success ? userTabsResult.data!.length : 0;

      return {
        success: true,
        data: {
          isHidden,
          hiddenTabsCount: hiddenTabIds.length,
          userTabsCount,
        },
      };
    } catch (error) {
      console.error('❌ 获取用户标签页显示状态失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user tabs visibility state',
          details: error,
        },
      };
    }
  }
}
