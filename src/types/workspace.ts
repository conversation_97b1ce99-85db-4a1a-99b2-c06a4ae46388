/**
 * 网站信息接口
 */
export interface Website {
  id: string;
  url: string;
  title: string;
  favicon: string;
  isPinned: boolean;
  addedAt: number;
  order: number; // 排序字段


}

/**
 * 工作区接口
 */
export interface WorkSpace {
  id: string;
  name: string;
  icon: string;
  color: string;
  websites: Website[];
  createdAt: number;
  updatedAt: number;
  isActive: boolean;
  order: number; // 排序字段
  windowId?: number; // 专用窗口ID（可选，用于专用窗口架构）
  userTabsHidden?: boolean; // 用户标签页是否已隐藏
  hiddenUserTabIds?: number[]; // 已隐藏的用户标签页ID列表
}

/**
 * 用户设置接口
 */
export interface Settings {
  autoCloseOtherTabs: boolean;
  preserveUserOpenedTabs: boolean;
  defaultWorkspaceOnStartup: string;
  sidebarWidth: number;
  theme: 'dark' | 'light' | 'auto';
  showFavicons: boolean;
  confirmBeforeDelete: boolean;
  maxRecentWorkspaces: number;
}

/**
 * 存储数据结构
 */
export interface StorageData {
  workspaces: WorkSpace[];
  settings: Settings;
  activeWorkspaceId: string | null;
  lastActiveWorkspaceIds: string[]; // 最近使用的工作区ID列表
}

/**
 * 标签页信息接口
 */
export interface TabInfo {
  id: number;
  url: string;
  title: string;
  favicon: string;
  isPinned: boolean;
  isActive: boolean;
  windowId: number;

}

/**
 * 工作区切换选项
 */
export interface WorkspaceSwitchOptions {
  closeOtherTabs?: boolean;
  preserveUserOpenedTabs?: boolean;
  focusFirstTab?: boolean; // 默认为 false，不自动聚焦到第一个标签页
}

/**
 * 网站添加选项
 */
export interface AddWebsiteOptions {
  title?: string;
  favicon?: string;
  pinTab?: boolean;
  openInNewTab?: boolean;
}

/**
 * 工作区创建选项
 */
export interface CreateWorkspaceOptions {
  name: string;
  icon?: string;
  color?: string;
  websites?: Omit<Website, 'id' | 'addedAt' | 'order'>[];
  activate?: boolean;
}

/**
 * 工作区更新选项
 */
export interface UpdateWorkspaceOptions {
  name?: string;
  icon?: string;
  color?: string;
  websites?: Website[];
  isActive?: boolean;
}

/**
 * 错误类型
 */
export interface WorkspaceError {
  code: string;
  message: string;
  details?: any;
}

/**
 * 操作结果接口
 */
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: WorkspaceError;
}

/**
 * 事件类型
 */
export type WorkspaceEventType = 
  | 'workspace-created'
  | 'workspace-updated'
  | 'workspace-deleted'
  | 'workspace-switched'
  | 'website-added'
  | 'website-removed'
  | 'website-updated'
  | 'settings-updated';

/**
 * 事件数据接口
 */
export interface WorkspaceEvent {
  type: WorkspaceEventType;
  timestamp: number;
  data: any;
}

/**
 * 预设工作区模板
 */
export interface WorkspaceTemplate {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  websites: Omit<Website, 'id' | 'addedAt' | 'order'>[];
  category: 'development' | 'design' | 'research' | 'productivity' | 'ai-tools' | 'custom';
}

/**
 * 导入导出数据格式
 */
export interface ExportData {
  version: string;
  exportedAt: number;
  workspaces: WorkSpace[];
  settings: Partial<Settings>;
}
