# 工作区切换自动聚焦行为修复

## 问题描述

**当前问题**：
- 工作区1的首个网站是 www.baidu.com
- 从工作区1切换到工作区2，再切换回工作区1时
- 系统会自动激活并定位到 www.baidu.com 标签页

**期望行为**：
- 工作区切换时不要自动激活任何特定标签页
- 保持用户当前的标签页焦点状态
- 或者保持切换前的标签页状态，而不是强制跳转到工作区的首个网站

## 修复内容

### 1. 修改默认的 focusFirstTab 行为
- **文件**: `src/utils/workspaceSwitcher.ts`
- **修改**: 将 `focusFirstTab` 的默认值从 `true` 改为 `false`
- **位置**: 第50行

```typescript
// 修改前
focusFirstTab: options.focusFirstTab ?? true,

// 修改后  
focusFirstTab: options.focusFirstTab ?? false, // 默认不自动聚焦到第一个标签页
```

### 2. 完全移除自动聚焦逻辑
- **文件**: `src/utils/workspaceSwitcher.ts`
- **修改**: 移除第84-91行的自动聚焦代码
- **替换为**: 注释说明新的行为

```typescript
// 修改前
// 7. 如果需要，聚焦到第一个标签页
if (switchOptions.focusFirstTab && workspace.websites.length > 0) {
  const firstWebsite = workspace.websites[0];
  const tabResult = await TabManager.findTabByUrl(firstWebsite.url);
  if (tabResult.success && tabResult.data) {
    await TabManager.activateTab(tabResult.data.id);
  }
}

// 修改后
// 7. 移除自动聚焦逻辑 - 保持用户当前的标签页焦点状态
// 不再自动激活工作区的第一个网站标签页，让用户保持当前的浏览状态
```

### 3. 更新类型定义注释
- **文件**: `src/types/workspace.ts`
- **修改**: 在 `WorkspaceSwitchOptions` 接口中添加注释说明默认行为

```typescript
export interface WorkspaceSwitchOptions {
  closeOtherTabs?: boolean;
  preserveUserOpenedTabs?: boolean;
  focusFirstTab?: boolean; // 默认为 false，不自动聚焦到第一个标签页
}
```

## 影响范围分析

### 保持现有行为的调用
以下调用已经显式设置了 `focusFirstTab: false`，不受影响：
1. **后台监听器** (`src/background/background.ts` 第95行)
   - 标签页激活时的自动工作区检测
   - 已经正确设置为 `false`

### 受益于修改的调用
以下调用没有显式设置 `focusFirstTab`，现在会使用新的默认值 `false`：
1. **UI手动切换** (`src/hooks/useWorkspaces.ts` 第136行)
   - 用户在侧边栏点击工作区切换
2. **快捷键切换** (`src/background/background.ts` 第169行)
   - 使用快捷键切换工作区
3. **下一个工作区切换** (`src/utils/workspaceSwitcher.ts` 第543行)
   - 循环切换到下一个工作区

## 测试场景

### 测试场景1：手动工作区切换
1. 在工作区1中打开多个标签页，激活非首个网站的标签页
2. 切换到工作区2
3. 再切换回工作区1
4. **验证**: 不应该自动跳转到 www.baidu.com，应该保持之前的标签页状态

### 测试场景2：快捷键工作区切换
1. 使用快捷键切换工作区
2. **验证**: 不应该自动激活工作区的首个网站

### 测试场景3：循环切换工作区
1. 使用 `switchToNextWorkspace` 方法
2. **验证**: 不应该自动激活工作区的首个网站

### 测试场景4：自动工作区检测
1. 激活某个标签页触发自动工作区检测
2. **验证**: 应该保持当前激活的标签页，不切换焦点

## 预期结果

### 修复前的行为
- 工作区切换后自动跳转到工作区的首个网站
- 用户的浏览状态被打断
- 需要手动切换回之前查看的标签页

### 修复后的行为
- 工作区切换后保持用户当前的浏览状态
- 不会自动激活任何特定标签页
- 用户体验更加流畅，不会被意外打断

## 向后兼容性

如果某些特殊场景需要自动聚焦到第一个标签页，可以在调用时显式传递 `focusFirstTab: true`：

```typescript
await WorkspaceSwitcher.switchToWorkspace(workspaceId, {
  focusFirstTab: true  // 显式启用自动聚焦
});
```

但根据用户反馈，这种自动聚焦行为通常是不需要的，所以默认禁用是更好的选择。
