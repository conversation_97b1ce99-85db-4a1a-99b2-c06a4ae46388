const STORAGE_KEYS = {
  WORKSPACES: "workspaces",
  SETTINGS: "settings",
  ACTIVE_WORKSPACE_ID: "activeWorkspaceId",
  LAST_ACTIVE_WORKSPACE_IDS: "lastActiveWorkspaceIds"
};
const DEFAULT_SETTINGS = {
  autoCloseOtherTabs: true,
  preserveUserOpenedTabs: false,
  defaultWorkspaceOnStartup: "",
  sidebarWidth: 320,
  theme: "dark",
  showFavicons: true,
  confirmBeforeDelete: true,
  maxRecentWorkspaces: 5
};
const WORKSPACE_COLORS = [
  "#3b82f6",
  // blue
  "#10b981",
  // emerald
  "#f59e0b",
  // amber
  "#ef4444",
  // red
  "#8b5cf6",
  // violet
  "#06b6d4",
  // cyan
  "#84cc16",
  // lime
  "#f97316",
  // orange
  "#ec4899",
  // pink
  "#6366f1"
  // indigo
];
const WORKSPACE_ICONS = [
  "🚀",
  "💼",
  "🔬",
  "🎨",
  "📊",
  "🛠️",
  "📚",
  "💡",
  "🎯",
  "⚡",
  "🌟",
  "🔥",
  "💎",
  "🎪",
  "🎭",
  "🎨",
  "🎵",
  "🎮",
  "🏆",
  "🎊",
  "📱",
  "💻",
  "🖥️",
  "⌨️",
  "🖱️",
  "🖨️",
  "📷",
  "📹",
  "🎥",
  "📺",
  "🔍",
  "🔎",
  "🔬",
  "🔭",
  "📡",
  "🛰️",
  "🚁",
  "✈️",
  "🛸"
];
const ERROR_CODES = {
  WORKSPACE_NOT_FOUND: "WORKSPACE_NOT_FOUND",
  WEBSITE_NOT_FOUND: "WEBSITE_NOT_FOUND",
  STORAGE_ERROR: "STORAGE_ERROR",
  TAB_ERROR: "TAB_ERROR",
  WINDOW_ERROR: "WINDOW_ERROR",
  PERMISSION_DENIED: "PERMISSION_DENIED",
  INVALID_URL: "INVALID_URL",
  DUPLICATE_WORKSPACE: "DUPLICATE_WORKSPACE",
  DUPLICATE_WEBSITE: "DUPLICATE_WEBSITE"
};
const COMMANDS = {
  SWITCH_WORKSPACE_1: "switch-workspace-1",
  SWITCH_WORKSPACE_2: "switch-workspace-2",
  SWITCH_WORKSPACE_3: "switch-workspace-3",
  TOGGLE_SIDEPANEL: "toggle-sidepanel"
};
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
const DEFAULT_FAVICON = 'data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m9 12 2 2 4-4"/></svg>';

class StorageManager {
  /**
   * 获取所有存储数据
   */
  static async getAllData() {
    try {
      const result = await chrome.storage.local.get([
        STORAGE_KEYS.WORKSPACES,
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.ACTIVE_WORKSPACE_ID,
        STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS
      ]);
      const data = {
        workspaces: result[STORAGE_KEYS.WORKSPACES] || [],
        settings: { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] },
        activeWorkspaceId: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null,
        lastActiveWorkspaceIds: result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || []
      };
      return { success: true, data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get storage data",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区
   */
  static async getWorkspaces() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.WORKSPACES);
      const workspaces = result[STORAGE_KEYS.WORKSPACES] || [];
      workspaces.sort((a, b) => a.order - b.order);
      return { success: true, data: workspaces };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 保存工作区列表
   */
  static async saveWorkspaces(workspaces) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.WORKSPACES]: workspaces
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 获取单个工作区
   */
  static async getWorkspace(id) {
    const result = await this.getWorkspaces();
    if (!result.success) {
      return {
        success: false,
        error: result.error
      };
    }
    const workspace = result.data.find((w) => w.id === id);
    if (!workspace) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WORKSPACE_NOT_FOUND,
          message: `Workspace with id ${id} not found`
        }
      };
    }
    return { success: true, data: workspace };
  }
  /**
   * 获取设置
   */
  static async getSettings() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.SETTINGS);
      const settings = { ...DEFAULT_SETTINGS, ...result[STORAGE_KEYS.SETTINGS] };
      return { success: true, data: settings };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get settings",
          details: error
        }
      };
    }
  }
  /**
   * 保存设置
   */
  static async saveSettings(settings) {
    try {
      const currentResult = await this.getSettings();
      if (!currentResult.success) {
        return {
          success: false,
          error: currentResult.error
        };
      }
      const updatedSettings = { ...currentResult.data, ...settings };
      await chrome.storage.local.set({
        [STORAGE_KEYS.SETTINGS]: updatedSettings
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to save settings",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃工作区ID
   */
  static async getActiveWorkspaceId() {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.ACTIVE_WORKSPACE_ID);
      return { success: true, data: result[STORAGE_KEYS.ACTIVE_WORKSPACE_ID] || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 设置当前活跃工作区ID
   */
  static async setActiveWorkspaceId(id) {
    try {
      await chrome.storage.local.set({
        [STORAGE_KEYS.ACTIVE_WORKSPACE_ID]: id
      });
      if (id) {
        await this.updateLastActiveWorkspaces(id);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set active workspace ID",
          details: error
        }
      };
    }
  }
  /**
   * 更新最近使用的工作区列表
   */
  static async updateLastActiveWorkspaces(workspaceId) {
    try {
      const result = await chrome.storage.local.get(STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS);
      let lastActiveIds = result[STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS] || [];
      lastActiveIds = lastActiveIds.filter((id) => id !== workspaceId);
      lastActiveIds.unshift(workspaceId);
      const settingsResult = await this.getSettings();
      const maxRecent = settingsResult.success ? settingsResult.data.maxRecentWorkspaces : 5;
      lastActiveIds = lastActiveIds.slice(0, maxRecent);
      await chrome.storage.local.set({
        [STORAGE_KEYS.LAST_ACTIVE_WORKSPACE_IDS]: lastActiveIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update last active workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 清除所有数据
   */
  static async clearAll() {
    try {
      await chrome.storage.local.clear();
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear storage",
          details: error
        }
      };
    }
  }
  /**
   * 监听存储变化
   */
  static onChanged(callback) {
    chrome.storage.onChanged.addListener((changes, areaName) => {
      if (areaName === "local") {
        callback(changes);
      }
    });
  }
  /**
   * 导出数据
   */
  static async exportData() {
    try {
      const dataResult = await this.getAllData();
      if (!dataResult.success) {
        return {
          success: false,
          error: dataResult.error
        };
      }
      const exportData = {
        version: "1.0.0",
        exportedAt: Date.now(),
        workspaces: dataResult.data.workspaces,
        settings: dataResult.data.settings
      };
      return { success: true, data: JSON.stringify(exportData, null, 2) };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to export data",
          details: error
        }
      };
    }
  }
  /**
   * 导入数据
   */
  static async importData(jsonData) {
    try {
      const importData = JSON.parse(jsonData);
      if (importData.workspaces) {
        await this.saveWorkspaces(importData.workspaces);
      }
      if (importData.settings) {
        await this.saveSettings(importData.settings);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to import data",
          details: error
        }
      };
    }
  }
}

const storage = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  StorageManager
}, Symbol.toStringTag, { value: 'Module' }));

const scriptRel = 'modulepreload';const assetsURL = function(dep) { return "/"+dep };const seen = {};const __vitePreload = function preload(baseModule, deps, importerUrl) {
  let promise = Promise.resolve();
  if (true && deps && deps.length > 0) {
    document.getElementsByTagName("link");
    const cspNonceMeta = document.querySelector(
      "meta[property=csp-nonce]"
    );
    const cspNonce = cspNonceMeta?.nonce || cspNonceMeta?.getAttribute("nonce");
    promise = Promise.allSettled(
      deps.map((dep) => {
        dep = assetsURL(dep);
        if (dep in seen) return;
        seen[dep] = true;
        const isCss = dep.endsWith(".css");
        const cssSelector = isCss ? '[rel="stylesheet"]' : "";
        if (document.querySelector(`link[href="${dep}"]${cssSelector}`)) {
          return;
        }
        const link = document.createElement("link");
        link.rel = isCss ? "stylesheet" : scriptRel;
        if (!isCss) {
          link.as = "script";
        }
        link.crossOrigin = "";
        link.href = dep;
        if (cspNonce) {
          link.setAttribute("nonce", cspNonce);
        }
        document.head.appendChild(link);
        if (isCss) {
          return new Promise((res, rej) => {
            link.addEventListener("load", res);
            link.addEventListener(
              "error",
              () => rej(new Error(`Unable to preload CSS for ${dep}`))
            );
          });
        }
      })
    );
  }
  function handlePreloadError(err) {
    const e = new Event("vite:preloadError", {
      cancelable: true
    });
    e.payload = err;
    window.dispatchEvent(e);
    if (!e.defaultPrevented) {
      throw err;
    }
  }
  return promise.then((res) => {
    for (const item of res || []) {
      if (item.status !== "rejected") continue;
      handlePreloadError(item.reason);
    }
    return baseModule().catch(handlePreloadError);
  });
};

class WindowManager {
  static globalWorkspaceWindowId = null;
  // 全局专用窗口ID
  static GLOBAL_WORKSPACE_WINDOW_KEY = "global_workspace_window";
  static isCreatingWindow = false;
  // 防止并发创建窗口
  /**
   * 从存储中恢复全局窗口ID
   */
  static async loadGlobalWindowId() {
    try {
      const result = await chrome.storage.local.get([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      if (result[this.GLOBAL_WORKSPACE_WINDOW_KEY]) {
        this.globalWorkspaceWindowId = result[this.GLOBAL_WORKSPACE_WINDOW_KEY];
      }
    } catch (error) {
      console.warn("加载全局窗口ID失败:", error);
    }
  }
  /**
   * 保存全局窗口ID到存储
   */
  static async saveGlobalWindowId(windowId) {
    try {
      await chrome.storage.local.set({
        [this.GLOBAL_WORKSPACE_WINDOW_KEY]: windowId
      });
      this.globalWorkspaceWindowId = windowId;
    } catch (error) {
      console.warn("保存全局窗口ID失败:", error);
    }
  }
  /**
   * 清理全局窗口ID
   */
  static async clearGlobalWindowId() {
    try {
      await chrome.storage.local.remove([this.GLOBAL_WORKSPACE_WINDOW_KEY]);
      this.globalWorkspaceWindowId = null;
    } catch (error) {
      console.warn("清理全局窗口ID失败:", error);
    }
  }
  /**
   * 获取或创建全局专用窗口（单例模式）
   */
  static async getOrCreateGlobalWorkspaceWindow() {
    try {
      console.log("🪟 获取或创建全局工作区专用窗口");
      if (this.isCreatingWindow) {
        console.log("⏳ 正在创建窗口中，等待完成...");
        await new Promise((resolve) => setTimeout(resolve, 1e3));
        return await this.getOrCreateGlobalWorkspaceWindow();
      }
      if (!this.globalWorkspaceWindowId) {
        await this.loadGlobalWindowId();
      }
      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId);
          if (window) {
            console.log(`✅ 全局工作区专用窗口已存在: ${this.globalWorkspaceWindowId}`);
            return {
              success: true,
              data: {
                id: this.globalWorkspaceWindowId,
                workspaceId: "global",
                workspaceName: "全局工作区专用窗口",
                tabCount: window.tabs?.length || 0,
                isVisible: window.state !== "minimized"
              }
            };
          }
        } catch {
          console.log("🗑️ 全局专用窗口已不存在，需要重新创建");
          await this.clearGlobalWindowId();
        }
      }
      this.isCreatingWindow = true;
      try {
        console.log("🔨 创建新的全局工作区专用窗口");
        const window = await chrome.windows.create({
          type: "normal",
          state: "normal",
          focused: false,
          // 不获取焦点
          width: 1200,
          height: 800,
          left: 100,
          top: 100,
          url: chrome.runtime.getURL("workspace-placeholder.html") + "?workspaceId=global&workspaceName=" + encodeURIComponent("全局工作区专用窗口")
        });
        if (!window.id) {
          throw new Error("Failed to create global workspace window");
        }
        await this.saveGlobalWindowId(window.id);
        try {
          await chrome.windows.update(window.id, { state: "minimized" });
        } catch (error) {
          console.warn("最小化窗口失败，但窗口创建成功:", error);
        }
      } finally {
        this.isCreatingWindow = false;
      }
      console.log(`✅ 成功创建全局工作区专用窗口 -> 窗口ID ${this.globalWorkspaceWindowId}`);
      return {
        success: true,
        data: {
          id: this.globalWorkspaceWindowId,
          workspaceId: "global",
          workspaceName: "全局工作区专用窗口",
          tabCount: 1,
          // 新创建的窗口包含一个占位符标签页
          isVisible: false
          // 窗口默认最小化，所以不可见
        }
      };
    } catch (error) {
      console.error(`创建全局工作区专用窗口失败`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to create global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 为工作区创建专用窗口（保持向后兼容）
   * 现在所有工作区都使用同一个全局专用窗口
   */
  static async createWorkspaceWindow(workspaceId, workspaceName) {
    return await this.getOrCreateGlobalWorkspaceWindow();
  }
  /**
   * 获取工作区的专用窗口ID（现在所有工作区共享同一个窗口）
   */
  static getWorkspaceWindowId(workspaceId) {
    return this.globalWorkspaceWindowId || void 0;
  }
  /**
   * 获取窗口对应的工作区ID（现在返回全局标识）
   */
  static getWindowWorkspaceId(windowId) {
    return windowId === this.globalWorkspaceWindowId ? "global" : void 0;
  }
  /**
   * 获取全局专用窗口ID
   */
  static getGlobalWorkspaceWindowId() {
    return this.globalWorkspaceWindowId || void 0;
  }
  /**
   * 将标签页移动到全局专用窗口
   */
  static async moveTabsToWorkspaceWindow(tabIds, workspaceId, workspaceName) {
    try {
      if (tabIds.length === 0) {
        return { success: true };
      }
      console.log(`🔄 开始移动 ${tabIds.length} 个标签页到全局专用窗口（来自工作区: ${workspaceName}）`, {
        tabIds,
        workspaceId,
        timestamp: (/* @__PURE__ */ new Date()).toISOString()
      });
      const windowResult = await this.getOrCreateGlobalWorkspaceWindow();
      if (!windowResult.success) {
        return { success: false, error: windowResult.error };
      }
      const windowId = windowResult.data.id;
      console.log(`🔍 开始检查 ${tabIds.length} 个标签页的固定状态...`);
      const pinnedTabsCount = [];
      const validTabIds = [];
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          validTabIds.push(tabId);
          console.log(`📋 检查标签页 ${tabId}: "${tab.title}" (${tab.url}) - 固定状态: ${tab.pinned}`);
          if (tab.pinned) {
            console.log(`📌 正在取消固定标签页 ${tabId} (${tab.title})...`);
            await chrome.tabs.update(tabId, { pinned: false });
            pinnedTabsCount.push(tabId);
            console.log(`✅ 成功取消固定标签页 ${tabId} (${tab.title}) 以便移动到专用窗口`);
          } else {
            console.log(`📄 标签页 ${tabId} (${tab.title}) 未固定，无需处理`);
          }
        } catch (error) {
          console.warn(`⚠️ 标签页 ${tabId} 不存在或无法访问，跳过移动:`, error);
        }
      }
      if (validTabIds.length === 0) {
        console.log(`⚠️ 没有有效的标签页需要移动`);
        return { success: true };
      }
      if (pinnedTabsCount.length > 0) {
        console.log(`📌 已取消固定 ${pinnedTabsCount.length} 个标签页，准备移动到专用窗口`);
      }
      try {
        if (validTabIds.length === 0) {
          console.log(`⚠️ 验证后发现没有有效的标签页需要移动`);
          return { success: true };
        }
        const safeTabIds = validTabIds.filter((id) => id && typeof id === "number" && id > 0);
        if (safeTabIds.length === 0) {
          console.log(`⚠️ 过滤后没有安全的标签页ID可以移动`);
          return { success: true };
        }
        if (safeTabIds.length !== validTabIds.length) {
          console.warn(`⚠️ 发现 ${validTabIds.length - safeTabIds.length} 个无效的标签页ID，已过滤`);
        }
        await chrome.tabs.move(safeTabIds, {
          windowId,
          index: -1
          // 移动到窗口末尾
        });
        console.log(`✅ 成功移动 ${safeTabIds.length} 个标签页到专用窗口 ${windowId}${pinnedTabsCount.length > 0 ? ` (其中 ${pinnedTabsCount.length} 个原本是固定的)` : ""}`);
      } catch (moveError) {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveError);
        throw moveError;
      }
      return { success: true };
    } catch (error) {
      console.error(`移动标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 将特定工作区的标签页从全局专用窗口移动到主窗口
   */
  static async moveTabsFromWorkspaceWindow(workspaceId, targetWindowId) {
    try {
      console.log(`从全局专用窗口移动工作区 ${workspaceId} 的标签页到主窗口`);
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在`);
        return { success: true, data: [] };
      }
      const { StorageManager } = await __vitePreload(async () => { const { StorageManager } = await Promise.resolve().then(() => storage);return { StorageManager }},true?void 0:void 0);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        console.log(`获取工作区 ${workspaceId} 信息失败:`, workspaceResult.error);
        return { success: true, data: [] };
      }
      const workspace = workspaceResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const tabs = await chrome.tabs.query({ windowId });
      const workspaceTabs = tabs.filter((tab) => {
        if (tab.url?.includes("workspace-placeholder.html")) {
          return false;
        }
        return workspaceUrls.some((url) => tab.url?.startsWith(url));
      });
      console.log(`在全局专用窗口中找到工作区 "${workspace.name}" 的 ${workspaceTabs.length} 个标签页`);
      if (workspaceTabs.length === 0) {
        console.log(`全局专用窗口中没有工作区 "${workspace.name}" 的标签页需要移动`);
        return { success: true, data: [] };
      }
      let targetWindow = targetWindowId;
      if (!targetWindow) {
        const currentWindow = await chrome.windows.getCurrent();
        targetWindow = currentWindow.id;
      }
      console.log(`从全局专用窗口 ${windowId} 移动 ${workspaceTabs.length} 个标签页到窗口 ${targetWindow}`);
      const tabIds = workspaceTabs.map((tab) => tab.id);
      const tabsToRepin = [];
      for (const tab of workspaceTabs) {
        if (tab.pinned) {
          tabsToRepin.push(tab.id);
          try {
            await chrome.tabs.update(tab.id, { pinned: false });
            console.log(`📌 取消固定标签页 ${tab.id} 以便移动`);
          } catch (error) {
            console.warn(`取消固定标签页 ${tab.id} 失败:`, error);
          }
        }
      }
      await chrome.tabs.move(tabIds, {
        windowId: targetWindow,
        index: -1
      });
      for (const tabId of tabsToRepin) {
        try {
          await chrome.tabs.update(tabId, { pinned: true });
          console.log(`📌 重新固定标签页 ${tabId}`);
        } catch (error) {
          console.warn(`重新固定标签页 ${tabId} 失败:`, error);
        }
      }
      const tabInfos = workspaceTabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: targetWindow
      }));
      console.log(`成功移动 ${workspaceTabs.length} 个标签页到主窗口`);
      return { success: true, data: tabInfos };
    } catch (error) {
      console.error(`从全局专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 关闭全局专用窗口
   */
  static async closeWorkspaceWindow(workspaceId) {
    return await this.closeGlobalWorkspaceWindow();
  }
  /**
   * 关闭全局专用窗口
   */
  static async closeGlobalWorkspaceWindow() {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        console.log(`全局专用窗口不存在，无需关闭`);
        return { success: true };
      }
      console.log(`关闭全局专用窗口: ${windowId}`);
      await this.moveTabsFromWorkspaceWindow("global");
      await chrome.windows.remove(windowId);
      this.globalWorkspaceWindowId = null;
      console.log(`成功关闭全局专用窗口: ${windowId}`);
      return { success: true };
    } catch (error) {
      console.error(`关闭全局专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to close global workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 获取所有工作区专用窗口信息（现在只有一个全局窗口）
   */
  static async getAllWorkspaceWindows() {
    try {
      const windowInfos = [];
      if (this.globalWorkspaceWindowId) {
        try {
          const window = await chrome.windows.get(this.globalWorkspaceWindowId, { populate: true });
          windowInfos.push({
            id: this.globalWorkspaceWindowId,
            workspaceId: "global",
            workspaceName: "全局工作区专用窗口",
            tabCount: window.tabs?.length || 0,
            isVisible: window.state !== "minimized"
          });
        } catch {
          this.globalWorkspaceWindowId = null;
        }
      }
      return { success: true, data: windowInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to get workspace windows",
          details: error
        }
      };
    }
  }
  /**
   * 更新窗口标题（现在更新全局窗口标题）
   */
  static async updateWindowTitle(workspaceId, workspaceName, tabCount) {
    try {
      const windowId = this.globalWorkspaceWindowId;
      if (!windowId) {
        return { success: true };
      }
      const tabs = await chrome.tabs.query({ windowId });
      const placeholderTab = tabs.find(
        (tab) => tab.url?.includes("workspace-placeholder.html")
      );
      if (placeholderTab) {
        const newUrl = chrome.runtime.getURL("workspace-placeholder.html") + `?workspaceId=global&workspaceName=${encodeURIComponent("全局工作区专用窗口")}&tabCount=${tabCount}`;
        await chrome.tabs.update(placeholderTab.id, { url: newUrl });
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.WINDOW_ERROR,
          message: "Failed to update window title",
          details: error
        }
      };
    }
  }
}

const windowManager = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WindowManager
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceManager {
  /**
   * 生成唯一ID
   */
  static generateId() {
    return `ws_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 生成网站ID
   */
  static generateWebsiteId() {
    return `site_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  /**
   * 验证URL格式
   */
  static isValidUrl(url) {
    return URL_REGEX.test(url);
  }
  /**
   * 获取网站favicon
   */
  static async getFavicon(url) {
    try {
      const domain = new URL(url).origin;
      return `${domain}/favicon.ico`;
    } catch {
      return DEFAULT_FAVICON;
    }
  }
  /**
   * 获取网站标题
   */
  static async getWebsiteTitle(url) {
    try {
      const tabs = await chrome.tabs.query({ url });
      if (tabs.length > 0 && tabs[0].title) {
        return tabs[0].title;
      }
      const domain = new URL(url).hostname;
      return domain.replace("www.", "");
    } catch {
      return url;
    }
  }
  /**
   * 创建新工作区
   */
  static async createWorkspace(options) {
    try {
      if (!options.name.trim()) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Workspace name cannot be empty"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const existingWorkspaces = workspacesResult.data;
      if (existingWorkspaces.some((w) => w.name === options.name)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WORKSPACE,
            message: "Workspace with this name already exists"
          }
        };
      }
      const workspace = {
        id: this.generateId(),
        name: options.name,
        icon: options.icon || WORKSPACE_ICONS[Math.floor(Math.random() * WORKSPACE_ICONS.length)],
        color: options.color || WORKSPACE_COLORS[Math.floor(Math.random() * WORKSPACE_COLORS.length)],
        websites: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isActive: false,
        order: existingWorkspaces.length
      };
      if (options.websites) {
        for (let i = 0; i < options.websites.length; i++) {
          const siteData = options.websites[i];
          const website = {
            id: this.generateWebsiteId(),
            url: siteData.url,
            title: siteData.title || await this.getWebsiteTitle(siteData.url),
            favicon: siteData.favicon || await this.getFavicon(siteData.url),
            isPinned: siteData.isPinned,
            addedAt: Date.now(),
            order: i
          };
          workspace.websites.push(website);
        }
      }
      existingWorkspaces.push(workspace);
      const saveResult = await StorageManager.saveWorkspaces(existingWorkspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      if (options.activate) {
        await StorageManager.setActiveWorkspaceId(workspace.id);
        workspace.isActive = true;
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to create workspace",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区
   */
  static async updateWorkspace(id, options) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const workspace = workspaces[workspaceIndex];
      if (options.name !== void 0) workspace.name = options.name;
      if (options.icon !== void 0) workspace.icon = options.icon;
      if (options.color !== void 0) workspace.color = options.color;
      if (options.websites !== void 0) workspace.websites = options.websites;
      if (options.isActive !== void 0) workspace.isActive = options.isActive;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      return { success: true, data: workspace };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update workspace",
          details: error
        }
      };
    }
  }
  /**
   * 删除工作区
   */
  static async deleteWorkspace(id) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspaceIndex = workspaces.findIndex((w) => w.id === id);
      if (workspaceIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${id} not found`
          }
        };
      }
      const closeWindowResult = await WindowManager.closeWorkspaceWindow(id);
      if (!closeWindowResult.success) {
      }
      workspaces.splice(workspaceIndex, 1);
      workspaces.forEach((workspace, index) => {
        workspace.order = index;
      });
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === id) {
        await StorageManager.setActiveWorkspaceId(null);
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to delete workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加网站到工作区
   */
  static async addWebsite(workspaceId, url, options = {}) {
    try {
      if (!this.isValidUrl(url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.INVALID_URL,
            message: "Invalid URL format"
          }
        };
      }
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      if (workspace.websites.some((w) => w.url === url)) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.DUPLICATE_WEBSITE,
            message: "Website with this URL already exists in workspace"
          }
        };
      }
      const websiteTitle = options.title || await this.getWebsiteTitle(url);
      const website = {
        id: this.generateWebsiteId(),
        url,
        title: websiteTitle,
        favicon: options.favicon || await this.getFavicon(url),
        isPinned: true,
        // 简化：工作区网站默认都是固定的
        addedAt: Date.now(),
        order: workspace.websites.length
      };
      workspace.websites.push(website);
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }
      if (options.openInNewTab) {
        await chrome.tabs.create({
          url,
          pinned: true
          // 简化：工作区标签页都是固定的
        });
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to add website",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区移除网站
   */
  static async removeWebsite(workspaceId, websiteId) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const websiteIndex = workspace.websites.findIndex((w) => w.id === websiteId);
      if (websiteIndex === -1) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      workspace.websites.splice(websiteIndex, 1);
      workspace.websites.forEach((website, index) => {
        website.order = index;
      });
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to remove website",
          details: error
        }
      };
    }
  }
  /**
   * 更新工作区中的网站
   */
  static async updateWebsite(workspaceId, websiteId, updates) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const website = workspace.websites.find((w) => w.id === websiteId);
      if (!website) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WEBSITE_NOT_FOUND,
            message: `Website with id ${websiteId} not found`
          }
        };
      }
      if (updates.url !== void 0) {
        if (!this.isValidUrl(updates.url)) {
          return {
            success: false,
            error: {
              code: ERROR_CODES.INVALID_URL,
              message: "Invalid URL format"
            }
          };
        }
        website.url = updates.url;
        website.favicon = await this.getFavicon(updates.url);
      }
      if (updates.title !== void 0) {
        website.title = updates.title;
      }
      if (updates.isPinned !== void 0) {
        website.isPinned = updates.isPinned;
      }
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      return { success: true, data: website };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to update website",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区
   */
  static async reorderWorkspaces(workspaceIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const reorderedWorkspaces = [];
      workspaceIds.forEach((id, index) => {
        const workspace = workspaces.find((w) => w.id === id);
        if (workspace) {
          workspace.order = index;
          reorderedWorkspaces.push(workspace);
        }
      });
      const saveResult = await StorageManager.saveWorkspaces(reorderedWorkspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder workspaces",
          details: error
        }
      };
    }
  }
  /**
   * 重新排序工作区内的网站
   */
  static async reorderWebsites(workspaceId, websiteIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: `Workspace with id ${workspaceId} not found`
          }
        };
      }
      const reorderedWebsites = [];
      websiteIds.forEach((id, index) => {
        const website = workspace.websites.find((w) => w.id === id);
        if (website) {
          website.order = index;
          reorderedWebsites.push(website);
        }
      });
      workspace.websites = reorderedWebsites;
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to reorder websites",
          details: error
        }
      };
    }
  }
  /**
   * 设置工作区用户标签页隐藏状态
   */
  static async setUserTabsHiddenState(workspaceId, isHidden, hiddenTabIds) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      workspace.userTabsHidden = isHidden;
      workspace.hiddenUserTabIds = hiddenTabIds || [];
      workspace.updatedAt = Date.now();
      const saveResult = await StorageManager.saveWorkspaces(workspaces);
      if (!saveResult.success) return saveResult;
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set user tabs hidden state",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区用户标签页隐藏状态
   */
  static async getUserTabsHiddenState(workspaceId) {
    try {
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return {
          success: false,
          error: workspaceResult.error
        };
      }
      const workspace = workspaceResult.data;
      return {
        success: true,
        data: {
          isHidden: workspace.userTabsHidden || false,
          hiddenTabIds: workspace.hiddenUserTabIds || []
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get user tabs hidden state",
          details: error
        }
      };
    }
  }
  /**
   * 清除工作区的隐藏标签页记录（当标签页被永久删除时调用）
   */
  static async clearHiddenTabIds(workspaceId, tabIdsToRemove) {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return {
          success: false,
          error: workspacesResult.error
        };
      }
      const workspaces = workspacesResult.data;
      const workspace = workspaces.find((w) => w.id === workspaceId);
      if (!workspace) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "Workspace not found"
          }
        };
      }
      if (workspace.hiddenUserTabIds) {
        workspace.hiddenUserTabIds = workspace.hiddenUserTabIds.filter(
          (id) => !tabIdsToRemove.includes(id)
        );
        workspace.updatedAt = Date.now();
        console.log(`从工作区 "${workspace.name}" 的隐藏列表中移除标签页ID: ${tabIdsToRemove.join(", ")}`);
        const saveResult = await StorageManager.saveWorkspaces(workspaces);
        if (!saveResult.success) return saveResult;
      }
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to clear hidden tab IDs",
          details: error
        }
      };
    }
  }
}

const workspace = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  WorkspaceManager
}, Symbol.toStringTag, { value: 'Module' }));

class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs() {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab() {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: "No active tab found"
          }
        };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get active tab",
          details: error
        }
      };
    }
  }
  /**
   * 检查URL是否已在当前窗口的标签页中打开（工作区隔离优化版）
   */
  static async findTabByUrl(url) {
    try {
      let tabs = await chrome.tabs.query({ url, currentWindow: true });
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const currentWindowTabs = await chrome.tabs.query({ currentWindow: true });
          tabs = currentWindowTabs.filter((tab2) => {
            if (!tab2.url) return false;
            try {
              const tabDomain = new URL(tab2.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          return { success: true, data: null };
        }
      }
      if (tabs.length === 0) {
        return { success: true, data: null };
      }
      const tab = tabs[0];
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to find tab by URL",
          details: error
        }
      };
    }
  }
  /**
   * 创建新标签页
   */
  static async createTab(url, pinned = false, active = true) {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active
      });
      const tabInfo = {
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      };
      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to create tab",
          details: error
        }
      };
    }
  }
  /**
   * 激活标签页
   */
  static async activateTab(tabId) {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to activate tab",
          details: error
        }
      };
    }
  }
  /**
   * 固定/取消固定标签页
   */
  static async pinTab(tabId, pinned) {
    try {
      await chrome.tabs.update(tabId, { pinned });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to pin/unpin tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭标签页
   */
  static async closeTab(tabId) {
    try {
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.pinned) {
          await chrome.tabs.update(tabId, { pinned: false });
        }
      } catch (error) {
        console.warn("取消标签页固定状态失败:", error);
      }
      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tab",
          details: error
        }
      };
    }
  }
  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds) {
    try {
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab.pinned) {
            await chrome.tabs.update(tabId, { pinned: false });
          }
        } catch (error) {
          console.warn(`取消标签页 ${tabId} 固定状态失败:`, error);
        }
      }
      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to close tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs() {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos = tabs.map((tab) => ({
        id: tab.id,
        url: tab.url || "",
        title: tab.title || "",
        favicon: tab.favIconUrl || "",
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId
      }));
      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get current window tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取工作区真正管理的标签页（仅在当前窗口中查找，排除用户自行打开的同URL标签页）
   */
  static async getWorkspaceRelatedTabs(workspace) {
    try {
      console.log(`查找工作区 "${workspace.name}" 真正管理的标签页`);
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const workspaceWebsites = workspace.websites;
      console.log(`工作区包含 ${workspaceWebsites.length} 个网站:`, workspaceWebsites.map((w) => `${w.url} (固定:${w.isPinned})`));
      const relatedTabs = [];
      for (const tab of currentTabs) {
        const matchingWebsite = workspaceWebsites.find((website) => {
          return tab.url.startsWith(website.url);
        });
        if (matchingWebsite) {
          if (tab.isPinned === matchingWebsite.isPinned) {
            console.log(`✅ 找到工作区管理的标签页(完全匹配): ${tab.title} (${tab.url}) - 固定状态: ${tab.isPinned}`);
            relatedTabs.push(tab);
          } else {
            console.log(`✅ 找到工作区管理的标签页(URL匹配): ${tab.title} (${tab.url}) - 工作区配置固定:${matchingWebsite.isPinned}, 当前固定:${tab.isPinned}`);
            relatedTabs.push(tab);
          }
        }
      }
      console.log(`工作区 "${workspace.name}" 真正管理的标签页共 ${relatedTabs.length} 个`);
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error("获取工作区相关标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace) {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const nonRelatedTabs = currentTabs.filter(
        (tab) => !workspaceUrls.some((url) => tab.url.startsWith(url))
      );
      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get non-workspace related tabs",
          details: error
        }
      };
    }
  }
  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId) {
    try {
      const tab = await chrome.tabs.get(tabId);
      return !tab.pinned;
    } catch {
      return false;
    }
  }
  /**
   * 获取当前工作区中用户自行打开的标签页（非工作区配置的标签页）
   */
  static async getUserOpenedTabs(workspace) {
    try {
      console.log(`查找工作区 "${workspace.name}" 中用户自行打开的标签页`);
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      console.log(`工作区配置的网站:`, workspaceUrls);
      const userOpenedTabs = currentTabs.filter((tab) => {
        if (tab.url.startsWith("chrome://") || tab.url.startsWith("chrome-extension://") || tab.url.startsWith("edge://") || tab.url.startsWith("about:")) {
          return false;
        }
        const isWorkspaceConfigured = workspaceUrls.some((url) => tab.url.startsWith(url));
        return !isWorkspaceConfigured;
      });
      console.log(`工作区 "${workspace.name}" 中用户自行打开的标签页共 ${userOpenedTabs.length} 个`);
      return { success: true, data: userOpenedTabs };
    } catch (error) {
      console.error("获取用户自行打开的标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get user opened tabs",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前工作区中配置的标签页（工作区设置中明确添加的网站标签页）
   */
  static async getWorkspaceConfiguredTabs(workspace) {
    try {
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;
      const currentTabs = currentTabsResult.data;
      const workspaceUrls = workspace.websites.map((w) => w.url);
      const configuredTabs = currentTabs.filter((tab) => {
        return workspaceUrls.some((url) => tab.url.startsWith(url));
      });
      console.log(`工作区 "${workspace.name}" 中配置的标签页共 ${configuredTabs.length} 个`);
      return { success: true, data: configuredTabs };
    } catch (error) {
      console.error("获取工作区配置的标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to get workspace configured tabs",
          details: error
        }
      };
    }
  }
}
class WorkspaceTabContentMatcher {
  /**
   * 检查标签页是否与工作区网站匹配（简化版：基于固定状态和域名匹配）
   */
  static async isWorkspaceTab(tab) {
    try {
      if (tab.url.includes("workspace-placeholder.html") || tab.url.includes("chrome-extension://") || tab.url.includes("chrome://") || tab.url.includes("edge://") || tab.url.includes("about:")) {
        return { isMatch: false };
      }
      if (!tab.isPinned) {
        return { isMatch: false };
      }
      const workspaceWebsites = await this.getAllWorkspaceWebsitesWithDetails();
      for (const website of workspaceWebsites) {
        if (this.isUrlMatch(tab.url, website.url)) {
          console.log(`🏢 通过固定状态+URL匹配识别为工作区标签页: ${tab.url} -> 工作区: ${website.workspaceId}`);
          return {
            isMatch: true,
            workspaceId: website.workspaceId,
            websiteId: website.websiteId
          };
        }
      }
      return { isMatch: false };
    } catch (error) {
      console.error("检查工作区标签页匹配失败:", error);
      return { isMatch: false };
    }
  }
  /**
   * 简单的URL匹配：使用前缀匹配
   */
  static isUrlMatch(tabUrl, websiteUrl) {
    try {
      const normalizeUrl = (url) => url.replace(/\/$/, "");
      const normalizedTabUrl = normalizeUrl(tabUrl);
      const normalizedWebsiteUrl = normalizeUrl(websiteUrl);
      return normalizedTabUrl.startsWith(normalizedWebsiteUrl);
    } catch (error) {
      console.error("URL匹配失败:", error);
      return false;
    }
  }
  /**
   * 获取所有工作区网站的详细信息（简化版）
   */
  static async getAllWorkspaceWebsitesWithDetails() {
    try {
      const { StorageManager } = await __vitePreload(async () => { const { StorageManager } = await Promise.resolve().then(() => storage);return { StorageManager }},true?void 0:void 0);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success || !workspacesResult.data) {
        return [];
      }
      const websites = [];
      for (const workspace of workspacesResult.data) {
        for (const website of workspace.websites) {
          websites.push({
            url: website.url,
            workspaceId: workspace.id,
            websiteId: website.id
          });
        }
      }
      return websites;
    } catch (error) {
      console.error("获取工作区网站详细信息失败:", error);
      return [];
    }
  }
}
class GlobalUserTabsVisibilityManager {
  /**
   * 检查标签页是否为真正的用户标签页（非工作区管理的标签页）
   * 简化版：基于固定状态的识别
   */
  /**
   * 检查是否为新标签页
   */
  static isNewTabPage(tab) {
    return tab.url === "chrome://newtab/" || tab.url === "chrome://new-tab-page/" || tab.url === "about:newtab" || tab.url === "edge://newtab/" || tab.url && tab.url.startsWith("chrome://newtab") || (tab.title === "New Tab" || tab.title === "新标签页" || tab.title === "Neuer Tab");
  }
  static async isRealUserTab(tab) {
    try {
      if (this.isNewTabPage(tab)) {
        console.log(`🚫 排除新标签页: ${tab.url} (${tab.title})`);
        return false;
      }
      if (!tab.isPinned) {
        console.log(`👤 识别为用户标签页（非固定）: ${tab.url} (${tab.title})`);
        return true;
      }
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(tab);
      if (matchResult.isMatch) {
        console.log(`🏢 识别为工作区管理的固定标签页: ${tab.url} (${tab.title}) - 工作区: ${matchResult.workspaceId}`);
        return false;
      } else {
        console.log(`👤 识别为用户固定的标签页: ${tab.url} (${tab.title})`);
        return true;
      }
    } catch (error) {
      console.error("检查用户标签页失败:", error);
      return true;
    }
  }
  /**
   * 获取全局用户标签页隐藏状态
   */
  static async getGlobalUserTabsState() {
    try {
      const result = await chrome.storage.local.get(["globalUserTabsHidden", "globalHiddenTabIds"]);
      const hiddenTabIds = result.globalHiddenTabIds || [];
      const validHiddenTabIds = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validHiddenTabIds.push(tabId);
        } catch {
        }
      }
      if (validHiddenTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          globalHiddenTabIds: validHiddenTabIds
        });
        if (validHiddenTabIds.length === 0) {
          await chrome.storage.local.set({
            globalUserTabsHidden: false
          });
        }
      }
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error
        };
      }
      const allTabs = allTabsResult.data;
      const allUserTabs = [];
      for (const tab of allTabs) {
        if (await this.isRealUserTab(tab)) {
          allUserTabs.push(tab);
        }
      }
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = allUserTabs.filter(
        (tab) => tab.windowId === currentWindow.id && !validHiddenTabIds.includes(tab.id)
      );
      const totalUserTabs = visibleUserTabs.length + validHiddenTabIds.length;
      const hasHiddenTabs = validHiddenTabIds.length > 0;
      const hasVisibleTabs = visibleUserTabs.length > 0;
      let actionType;
      let canContinueHiding = false;
      if (hasHiddenTabs && hasVisibleTabs) {
        actionType = "continue_hide";
        canContinueHiding = true;
      } else if (hasHiddenTabs && !hasVisibleTabs) {
        actionType = "show";
        canContinueHiding = false;
      } else {
        actionType = "hide";
        canContinueHiding = false;
      }
      return {
        success: true,
        data: {
          isHidden: hasHiddenTabs,
          hiddenTabIds: validHiddenTabIds,
          totalUserTabs,
          visibleUserTabs: visibleUserTabs.length,
          canContinueHiding,
          actionType
        }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get global user tabs state",
          details: error
        }
      };
    }
  }
  /**
   * 设置全局用户标签页隐藏状态
   */
  static async setGlobalUserTabsState(isHidden, hiddenTabIds) {
    try {
      await chrome.storage.local.set({
        globalUserTabsHidden: isHidden,
        globalHiddenTabIds: hiddenTabIds
      });
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to set global user tabs state",
          details: error
        }
      };
    }
  }
  /**
   * 继续隐藏当前可见的用户标签页（保留已隐藏的标签页）
   */
  static async continueHideUserTabs() {
    try {
      console.log("🔒 继续隐藏当前可见的用户标签页");
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const currentState = stateResult.data;
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error
        };
      }
      const allTabs = allTabsResult.data;
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = [];
      for (const tab of allTabs) {
        if (tab.windowId === currentWindow.id && await this.isRealUserTab(tab) && !currentState.hiddenTabIds.includes(tab.id)) {
          visibleUserTabs.push(tab);
        }
      }
      if (visibleUserTabs.length === 0) {
        console.log("⚠️ 没有新的用户标签页需要隐藏");
        return { success: true, data: [] };
      }
      console.log(`📤 准备继续隐藏 ${visibleUserTabs.length} 个可见的用户标签页`);
      const { WindowManager } = await __vitePreload(async () => { const { WindowManager } = await Promise.resolve().then(() => windowManager);return { WindowManager }},true?void 0:void 0);
      const newTabIds = visibleUserTabs.map((tab) => tab.id).filter((id) => id && typeof id === "number" && id > 0);
      if (newTabIds.length === 0) {
        console.warn("⚠️ 没有有效的标签页ID可以继续隐藏");
        return { success: true, data: [] };
      }
      if (newTabIds.length !== visibleUserTabs.length) {
        console.warn(`⚠️ 发现 ${visibleUserTabs.length - newTabIds.length} 个无效的标签页ID，已过滤`);
      }
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        newTabIds,
        "global-hidden-tabs",
        "隐藏的用户标签页"
      );
      if (!moveResult.success) {
        console.error("❌ 移动用户标签页到隐藏窗口失败:", moveResult.error);
        return {
          success: false,
          error: moveResult.error
        };
      }
      const allHiddenTabIds = [...currentState.hiddenTabIds, ...newTabIds];
      const saveResult = await this.setGlobalUserTabsState(true, allHiddenTabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      console.log(`✅ 成功继续隐藏 ${newTabIds.length} 个用户标签页`);
      return { success: true, data: newTabIds };
    } catch (error) {
      console.error("❌ 继续隐藏用户标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to continue hiding user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 全局隐藏所有用户标签页
   */
  static async hideAllUserTabs() {
    try {
      console.log("🔒 开始全局隐藏所有用户标签页");
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error
        };
      }
      const allTabs = allTabsResult.data;
      const currentWindow = await chrome.windows.getCurrent();
      const userTabs = [];
      for (const tab of allTabs) {
        if (tab.windowId === currentWindow.id && await this.isRealUserTab(tab)) {
          userTabs.push(tab);
        }
      }
      if (userTabs.length === 0) {
        console.log("⚠️ 没有用户标签页需要隐藏");
        return { success: true, data: [] };
      }
      const pinnedTabs = allTabs.filter(
        (tab) => tab.windowId === currentWindow.id && tab.isPinned
      );
      const existingNewTabs = allTabs.filter(
        (tab) => tab.windowId === currentWindow.id && this.isNewTabPage(tab)
      );
      const shouldCreateNewTab = userTabs.length === 1 && !this.isNewTabPage(userTabs[0]) && existingNewTabs.length === 0;
      if (shouldCreateNewTab) {
        const reason = pinnedTabs.length === 0 ? "没有固定标签页且只剩一个用户标签页" : "有固定标签页但只剩一个用户标签页";
        console.log(`📌 ${reason}，且没有现有新标签页，创建新标签页以保持窗口活跃`);
        try {
          await chrome.tabs.create({
            url: "chrome://newtab/",
            active: true
          });
          console.log("📌 已创建新标签页");
        } catch (error) {
          console.warn("创建新标签页失败:", error);
        }
      } else if (userTabs.length === 1 && !this.isNewTabPage(userTabs[0]) && existingNewTabs.length > 0) {
        console.log(`📌 只有一个用户标签页，但已存在 ${existingNewTabs.length} 个新标签页，无需重复创建`);
      }
      console.log(`📤 准备隐藏 ${userTabs.length} 个用户标签页（自动排除新标签页）`);
      const { WindowManager } = await __vitePreload(async () => { const { WindowManager } = await Promise.resolve().then(() => windowManager);return { WindowManager }},true?void 0:void 0);
      const tabIds = userTabs.map((tab) => tab.id).filter((id) => id && typeof id === "number" && id > 0);
      if (tabIds.length === 0) {
        console.warn("⚠️ 没有有效的标签页ID可以隐藏");
        return { success: true, data: [] };
      }
      if (tabIds.length !== userTabs.length) {
        console.warn(`⚠️ 发现 ${userTabs.length - tabIds.length} 个无效的标签页ID，已过滤`);
      }
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        "global-hidden-tabs",
        "隐藏的用户标签页"
      );
      if (!moveResult.success) {
        console.error("❌ 移动用户标签页到隐藏窗口失败:", moveResult.error);
        return {
          success: false,
          error: moveResult.error
        };
      }
      const saveResult = await this.setGlobalUserTabsState(true, tabIds);
      if (!saveResult.success) {
        return {
          success: false,
          error: saveResult.error
        };
      }
      console.log(`✅ 成功隐藏 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error("❌ 全局隐藏用户标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to hide all user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 全局显示所有隐藏的用户标签页
   */
  static async showAllUserTabs() {
    try {
      console.log("🔓 开始全局显示所有隐藏的用户标签页");
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const { isHidden, hiddenTabIds } = stateResult.data;
      if (!isHidden || hiddenTabIds.length === 0) {
        console.log("⚠️ 没有隐藏的用户标签页需要显示");
        return { success: true, data: [] };
      }
      console.log(`📥 准备显示 ${hiddenTabIds.length} 个隐藏的用户标签页`);
      const existingTabIds = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }
      if (existingTabIds.length === 0) {
        console.log("⚠️ 所有隐藏的标签页都已不存在");
        await this.setGlobalUserTabsState(false, []);
        return { success: true, data: [] };
      }
      const currentWindow = await chrome.windows.getCurrent();
      await chrome.tabs.move(existingTabIds, {
        windowId: currentWindow.id,
        index: -1
        // 移动到窗口末尾
      });
      const clearResult = await this.setGlobalUserTabsState(false, []);
      if (!clearResult.success) {
        return {
          success: false,
          error: clearResult.error
        };
      }
      console.log(`✅ 成功显示 ${existingTabIds.length} 个隐藏的用户标签页`);
      return { success: true, data: existingTabIds };
    } catch (error) {
      console.error("❌ 全局显示用户标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to show all user tabs",
          details: error
        }
      };
    }
  }
  /**
   * 切换全局用户标签页的显示/隐藏状态（智能模式）
   */
  static async toggleGlobalUserTabsVisibility() {
    try {
      console.log("🔄 智能切换全局用户标签页的显示状态");
      const stateResult = await this.getGlobalUserTabsState();
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error
        };
      }
      const { actionType } = stateResult.data;
      switch (actionType) {
        case "show":
          const showResult = await this.showAllUserTabs();
          if (!showResult.success) {
            return {
              success: false,
              error: showResult.error
            };
          }
          console.log(`✅ 全局用户标签页显示成功，影响 ${showResult.data.length} 个标签页`);
          return {
            success: true,
            data: {
              action: "shown",
              tabIds: showResult.data
            }
          };
        case "continue_hide":
          const continueHideResult = await this.continueHideUserTabs();
          if (!continueHideResult.success) {
            return {
              success: false,
              error: continueHideResult.error
            };
          }
          console.log(`✅ 继续隐藏用户标签页成功，影响 ${continueHideResult.data.length} 个标签页`);
          return {
            success: true,
            data: {
              action: "continue_hidden",
              tabIds: continueHideResult.data
            }
          };
        case "hide":
        default:
          const hideResult = await this.hideAllUserTabs();
          if (!hideResult.success) {
            return {
              success: false,
              error: hideResult.error
            };
          }
          console.log(`✅ 全局用户标签页隐藏成功，影响 ${hideResult.data.length} 个标签页`);
          return {
            success: true,
            data: {
              action: "hidden",
              tabIds: hideResult.data
            }
          };
      }
    } catch (error) {
      console.error("❌ 切换全局用户标签页显示状态失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to toggle global user tabs visibility",
          details: error
        }
      };
    }
  }
}

const tabs = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  GlobalUserTabsVisibilityManager,
  TabManager,
  WorkspaceTabContentMatcher
}, Symbol.toStringTag, { value: 'Module' }));

class WorkspaceSwitcher {
  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(workspaceId, options = {}) {
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }
      const workspace = workspaceResult.data;
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }
      const settings = settingsResult.data;
      const switchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false
        // 默认不自动聚焦到第一个标签页
      };
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        await this.ensureWindowHasUserTabsBeforeMove();
        await this.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        await this.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }
      await this.moveTabsFromWorkspaceWindow(workspace);
      await this.openWorkspaceWebsites(workspace);
      await this.handleUserTabsVisibilityState(workspace);
      await StorageManager.setActiveWorkspaceId(workspaceId);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data;
        workspaces.forEach((w) => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }
      await this.notifyWorkspaceSwitchComplete(workspaceId);
      console.log(`成功切换到工作区: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch workspace",
          details: error
        }
      };
    }
  }
  /**
   * 移动非目标工作区的标签页到专用窗口
   */
  static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId) {
    try {
      console.log(`检查并移动非目标工作区的标签页到专用窗口，目标工作区: ${targetWorkspaceId}`);
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.log("获取工作区列表失败:", workspacesResult.error);
        return { success: true };
      }
      const workspaces = workspacesResult.data;
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log("获取当前窗口标签页失败:", currentTabsResult.error);
        return { success: true };
      }
      const currentTabs = currentTabsResult.data;
      console.log(`当前窗口共有 ${currentTabs.length} 个标签页`);
      for (const workspace of workspaces) {
        if (workspace.id === targetWorkspaceId) {
          continue;
        }
        const relatedTabs = [];
        for (const tab of currentTabs) {
          const matchingWebsite = workspace.websites.find((website) => {
            if (!tab.url.startsWith(website.url)) return false;
            return tab.isPinned === website.isPinned;
          });
          if (matchingWebsite) {
            relatedTabs.push(tab);
          }
        }
        if (relatedTabs.length > 0) {
          console.log(`发现工作区 "${workspace.name}" 的 ${relatedTabs.length} 个真正管理的标签页需要移动到专用窗口`);
          const tabIds = relatedTabs.map((tab) => tab.id);
          const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
            tabIds,
            workspace.id,
            workspace.name
          );
          if (moveResult.success) {
            console.log(`成功移动工作区 "${workspace.name}" 的 ${tabIds.length} 个标签页到专用窗口`);
          } else {
            console.error(`移动工作区 "${workspace.name}" 的标签页失败:`, moveResult.error);
          }
        }
      }
      return { success: true };
    } catch (error) {
      console.error("移动非目标工作区标签页失败:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move non-target workspace tabs",
          details: error
        }
      };
    }
  }
  /**
   * 在移动标签页之前确保窗口有用户标签页，预防性创建保护标签页
   */
  static async ensureWindowHasUserTabsBeforeMove() {
    try {
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log("🔍 移动前窗口状态检查:", {
        总标签页: allTabs.length,
        固定标签页: allTabs.filter((tab) => tab.pinned).length,
        用户标签页: allTabs.filter((tab) => !tab.pinned).length
      });
      const pinnedTabs = allTabs.filter((tab) => tab.pinned);
      const userTabs = allTabs.filter((tab) => !tab.pinned);
      if (pinnedTabs.length > 0 && userTabs.length === 0) {
        console.log("⚠️ 检测到只有固定标签页，预先创建保护性标签页防止窗口关闭");
        await chrome.tabs.create({
          url: "chrome://newtab/",
          active: false
          // 不激活，避免干扰用户体验
        });
        console.log("✅ 已创建保护性标签页");
      } else if (userTabs.length > 0) {
        console.log("ℹ️ 窗口有用户标签页，无需创建保护性标签页");
      } else {
        console.log("ℹ️ 窗口没有固定标签页，无需保护");
      }
    } catch (error) {
      console.warn("移动前窗口状态检查失败:", error);
    }
  }
  /**
   * 确保窗口有用户标签页，如果只有固定标签页则创建新标签页
   */
  static async ensureWindowHasUserTabs() {
    try {
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const pinnedTabs = allTabs.filter((tab) => tab.pinned);
      const userTabs = allTabs.filter((tab) => !tab.pinned);
      if (pinnedTabs.length > 0 && userTabs.length === 0) {
        console.log("🔄 只有固定标签页，创建新标签页防止窗口关闭");
        await chrome.tabs.create({
          url: "chrome://newtab/",
          active: true
        });
        console.log("✅ 已创建新标签页");
      }
    } catch (error) {
      console.warn("检查窗口标签页状态失败:", error);
    }
  }
  /**
   * 将当前窗口的工作区相关标签页移动到专用窗口
   */
  static async moveCurrentTabsToWorkspaceWindow(workspace) {
    try {
      console.log(`将工作区 ${workspace.name} 的标签页移动到专用窗口`);
      console.log(`🔍 正在查找工作区 "${workspace.name}" 的相关标签页...`);
      const workspaceTabsResult = await TabManager.getWorkspaceRelatedTabs(workspace);
      if (!workspaceTabsResult.success) {
        console.log(`❌ 获取工作区相关标签页失败:`, workspaceTabsResult.error);
        return { success: true };
      }
      const workspaceTabs = workspaceTabsResult.data;
      console.log(
        `📊 找到 ${workspaceTabs.length} 个工作区相关标签页:`,
        workspaceTabs.map((tab) => `${tab.id}:${tab.title}(固定:${tab.isPinned})`)
      );
      if (workspaceTabs.length === 0) {
        console.log(`ℹ️ 工作区 ${workspace.name} 没有相关标签页需要移动`);
        return { success: true };
      }
      const allTabsPinned = workspaceTabs.every((tab) => tab.isPinned);
      const currentWindow = await chrome.windows.getCurrent();
      const allCurrentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      const remainingUserTabs = allCurrentTabs.filter(
        (tab) => !tab.pinned && !workspaceTabs.some((wTab) => wTab.id === tab.id)
      );
      console.log("📊 移动前状态分析:", {
        要移动的标签页: workspaceTabs.length,
        全部是固定标签页: allTabsPinned,
        移动后剩余用户标签页: remainingUserTabs.length
      });
      if (remainingUserTabs.length === 0 && allTabsPinned) {
        console.log("⚠️ 移动操作会导致只剩固定标签页，确保有保护性标签页");
        await this.ensureWindowHasUserTabsBeforeMove();
      }
      const tabIds = workspaceTabs.map((tab) => tab.id);
      console.log(`🚀 准备移动标签页到专用窗口:`, tabIds);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        workspace.id,
        workspace.name
      );
      if (moveResult.success) {
        console.log(`✅ 成功移动 ${tabIds.length} 个标签页到工作区专用窗口`);
        const finalTabs = await chrome.tabs.query({ windowId: currentWindow.id });
        console.log("📊 移动后窗口状态:", {
          剩余标签页: finalTabs.length,
          固定标签页: finalTabs.filter((tab) => tab.pinned).length,
          用户标签页: finalTabs.filter((tab) => !tab.pinned).length
        });
      } else {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveResult.error);
      }
      return moveResult;
    } catch (error) {
      console.error(`移动当前标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move current tabs to workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 从工作区专用窗口移动标签页到当前窗口（优化版）
   */
  static async moveTabsFromWorkspaceWindow(workspace) {
    try {
      console.log(`📥 从工作区 ${workspace.name} 的专用窗口移动标签页到主窗口`);
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);
      if (moveResult.success) {
        const movedTabs = moveResult.data;
        console.log(`✅ 成功从专用窗口移动 ${movedTabs.length} 个标签页到主窗口`);
        if (movedTabs.length > 0) {
          console.log("⏳ 等待标签页完全加载到主窗口...");
          await new Promise((resolve) => setTimeout(resolve, 50));
        }
      } else {
        console.error(`❌ 从专用窗口移动标签页失败:`, moveResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error(`❌ 从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to move tabs from workspace window",
          details: error
        }
      };
    }
  }
  /**
   * 智能检查并自动打开工作区中缺失的网站（修复版）
   */
  static async openWorkspaceWebsites(workspace) {
    try {
      console.log(`🔍 智能检查并打开工作区 ${workspace.name} 中缺失的网站`);
      if (!workspace.websites || workspace.websites.length === 0) {
        console.log(`⚠️ 工作区 "${workspace.name}" 没有配置任何网站`);
        const currentTabsResult2 = await TabManager.getCurrentWindowTabs();
        if (currentTabsResult2.success) {
          const currentTabs2 = currentTabsResult2.data;
          if (currentTabs2.length === 0) {
            console.log("📝 主窗口没有标签页，为空工作区创建默认新标签页");
            const newTabResult = await TabManager.createTab("chrome://newtab/", false, true);
            if (newTabResult.success) {
              console.log("✅ 为空工作区成功创建默认新标签页");
            } else {
              console.error("❌ 为空工作区创建默认新标签页失败:", newTabResult.error);
            }
          } else {
            console.log(`✅ 主窗口已有 ${currentTabs2.length} 个标签页，空工作区无需额外操作`);
          }
        }
        return { success: true };
      }
      console.log("⏳ 等待标签页移动操作完全完成...");
      await new Promise((resolve) => setTimeout(resolve, 100));
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log("❌ 获取当前窗口标签页失败:", currentTabsResult.error);
        return { success: true };
      }
      const currentTabs = currentTabsResult.data;
      const currentUrls = currentTabs.map((tab) => tab.url);
      console.log(`📋 当前窗口已有 ${currentTabs.length} 个标签页:`, currentUrls);
      const missingWebsites = [];
      const existingTabsToPin = [];
      for (const website of workspace.websites) {
        const existingTab = currentTabs.find((tab) => tab.url.startsWith(website.url));
        if (!existingTab) {
          missingWebsites.push(website);
          console.log(`🔍 发现缺失的网站: ${website.title} (${website.url})`);
        } else {
          if (!existingTab.isPinned) {
            existingTabsToPin.push({
              tab: existingTab,
              website
            });
            console.log(`📌 发现需要固定的标签页: ${website.title} (${website.url})`);
          } else {
            console.log(`✅ 网站已存在且已固定: ${website.title} (${website.url})`);
          }
        }
      }
      if (existingTabsToPin.length > 0) {
        console.log(`📌 正在固定 ${existingTabsToPin.length} 个已存在的标签页`);
        for (const { tab, website } of existingTabsToPin) {
          try {
            const pinResult = await TabManager.pinTab(tab.id, true);
            if (pinResult.success) {
              console.log(`✅ 已固定标签页: ${website.title} (${tab.url})`);
            } else {
              console.error(`❌ 固定标签页失败 ${website.title}:`, pinResult.error);
            }
          } catch (error) {
            console.error(`❌ 固定标签页异常 ${website.title}:`, error);
          }
        }
      }
      if (missingWebsites.length === 0) {
        console.log(`✅ 工作区 "${workspace.name}" 的所有网站都已打开${existingTabsToPin.length > 0 ? "并已固定" : ""}，无需创建新标签页`);
        if (currentTabs.length === 0) {
          console.log("⚠️ 主窗口没有标签页，创建默认新标签页");
          await this.ensureMainWindowHasTab();
        }
        return { success: true };
      }
      console.log(`🚀 需要打开 ${missingWebsites.length} 个缺失的网站`);
      let successCount = 0;
      let failCount = 0;
      for (const website of missingWebsites) {
        try {
          console.log(`📝 正在创建标签页: ${website.title} (${website.url})`);
          const newTabResult = await TabManager.createTab(
            website.url,
            true,
            // 简化：工作区标签页都是固定的
            false
            // 不立即激活，保持当前标签页活跃
          );
          if (newTabResult.success) {
            console.log(`✅ 成功创建标签页: ${website.title}`);
            successCount++;
          } else {
            console.error(`❌ 创建标签页失败 ${website.title}:`, newTabResult.error);
            failCount++;
          }
        } catch (error) {
          console.error(`❌ 处理网站 ${website.title} 时出错:`, error);
          failCount++;
        }
      }
      console.log(`🎯 工作区 "${workspace.name}" 缺失网站打开完成: 成功 ${successCount} 个，失败 ${failCount} 个`);
      return { success: true };
    } catch (error) {
      console.error("❌ 自动打开缺失网站时出错:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to open workspace websites",
          details: error
        }
      };
    }
  }
  /**
   * 确保主窗口至少有一个标签页
   */
  static async ensureMainWindowHasTab() {
    try {
      const newTabResult = await TabManager.createTab("chrome://newtab/", false, true);
      if (newTabResult.success) {
        console.log("✅ 成功创建默认新标签页");
      } else {
        console.error("❌ 创建默认新标签页失败:", newTabResult.error);
      }
      return { success: true };
    } catch (error) {
      console.error("❌ 确保主窗口有标签页时出错:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to ensure main window has tab",
          details: error
        }
      };
    }
  }
  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace() {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }
      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }
      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }
      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: "Failed to get current workspace",
          details: error
        }
      };
    }
  }
  /**
   * 智能检测当前应该激活的工作区
   */
  static async detectActiveWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }
      const activeTab = activeTabResult.data;
      const matchingWorkspace = workspaces.find(
        (workspace) => workspace.websites.some(
          (website) => activeTab.url.startsWith(website.url)
        )
      );
      return { success: true, data: matchingWorkspace || null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to detect active workspace",
          details: error
        }
      };
    }
  }
  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId) {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }
      const activeTab = activeTabResult.data;
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
          pinTab: activeTab.isPinned
        }
      );
      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to add current tab to workspace",
          details: error
        }
      };
    }
  }
  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace() {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }
      const workspaces = workspacesResult.data;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: "No workspaces available"
          }
        };
      }
      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }
      const currentWorkspace = currentResult.data;
      let nextIndex = 0;
      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex((w) => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }
      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to switch to next workspace",
          details: error
        }
      };
    }
  }
  /**
   * 处理工作区切换时的用户标签页隐藏状态
   */
  static async handleUserTabsVisibilityState(workspace$1) {
    try {
      console.log(`🔄 检查工作区 "${workspace$1.name}" 的用户标签页隐藏状态`);
      const { WorkspaceManager: WorkspaceManager2 } = await __vitePreload(async () => { const { WorkspaceManager: WorkspaceManager2 } = await Promise.resolve().then(() => workspace);return { WorkspaceManager: WorkspaceManager2 }},true?void 0:void 0);
      const stateResult = await WorkspaceManager2.getUserTabsHiddenState(workspace$1.id);
      if (!stateResult.success) {
        console.log("⚠️ 无法获取用户标签页隐藏状态，跳过处理");
        return { success: true };
      }
      const { isHidden, hiddenTabIds } = stateResult.data;
      if (isHidden && hiddenTabIds.length > 0) {
        console.log(`🔒 工作区 "${workspace$1.name}" 有 ${hiddenTabIds.length} 个隐藏的用户标签页，需要保持隐藏状态`);
        const existingTabIds = [];
        for (const tabId of hiddenTabIds) {
          try {
            await chrome.tabs.get(tabId);
            existingTabIds.push(tabId);
          } catch {
            console.log(`⚠️ 隐藏的标签页 ${tabId} 已不存在`);
          }
        }
        if (existingTabIds.length !== hiddenTabIds.length) {
          const removedTabIds = hiddenTabIds.filter((id) => !existingTabIds.includes(id));
          await WorkspaceManager2.clearHiddenTabIds(workspace$1.id, removedTabIds);
          if (existingTabIds.length === 0) {
            await WorkspaceManager2.setUserTabsHiddenState(workspace$1.id, false, []);
            console.log(`✅ 工作区 "${workspace$1.name}" 的所有隐藏标签页都已不存在，清除隐藏状态`);
          } else {
            await WorkspaceManager2.setUserTabsHiddenState(workspace$1.id, true, existingTabIds);
            console.log(`✅ 更新工作区 "${workspace$1.name}" 的隐藏标签页列表，现有 ${existingTabIds.length} 个`);
          }
        }
        if (existingTabIds.length > 0) {
          const currentWindow = await chrome.windows.getCurrent();
          const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
          const tabsToHide = currentTabs.filter((tab) => existingTabIds.includes(tab.id));
          if (tabsToHide.length > 0) {
            console.log(`📤 发现 ${tabsToHide.length} 个应该隐藏的标签页在主窗口中，移动到专用窗口`);
            const tabIdsToMove = tabsToHide.map((tab) => tab.id);
            const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
              tabIdsToMove,
              workspace$1.id,
              `${workspace$1.name} - 隐藏的用户标签页`
            );
            if (moveResult.success) {
              console.log(`✅ 成功移动 ${tabIdsToMove.length} 个标签页到专用窗口`);
            } else {
              console.error("❌ 移动隐藏标签页到专用窗口失败:", moveResult.error);
            }
          }
        }
      } else {
        console.log(`✅ 工作区 "${workspace$1.name}" 没有隐藏的用户标签页`);
      }
      return { success: true };
    } catch (error) {
      console.error("❌ 处理用户标签页隐藏状态时出错:", error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: "Failed to handle user tabs visibility state",
          details: error
        }
      };
    }
  }
  /**
   * 通知工作区切换完成，触发UI状态更新
   */
  static async notifyWorkspaceSwitchComplete(workspaceId) {
    try {
      const { WorkspaceStateSync } = await __vitePreload(async () => { const { WorkspaceStateSync } = await import('./workspaceStateSync-BUoJ_loS.js');return { WorkspaceStateSync }},true?[]:void 0);
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, "switch");
    } catch (error) {
      console.error("通知工作区切换完成失败:", error);
    }
  }
}

export { COMMANDS as C, StorageManager as S, URL_REGEX as U, WorkspaceManager as W, __vitePreload as _, WorkspaceSwitcher as a, WORKSPACE_ICONS as b, WORKSPACE_COLORS as c, tabs as t };
